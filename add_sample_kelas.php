<?php
/**
 * Add Sample Kelas Data for Testing
 */

require_once 'app/config/Database.php';

try {
    $db = new Database();
    
    // Sample kelas data
    $sampleKelas = [
        [
            'nama_kelas' => 'KPP A',
            'tingkat' => 'KPP',
            'jurusan' => 'Umum',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'Budi Santoso',
            'kapasitas' => 30,
            'is_active' => 1
        ],
        [
            'nama_kelas' => 'KPP B',
            'tingkat' => 'KPP',
            'jurusan' => 'Umum',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'Sari <PERSON>',
            'kapasitas' => 25,
            'is_active' => 1
        ],
        [
            'nama_kelas' => 'KPP C',
            'tingkat' => 'KPP',
            'jurusan' => 'Umum',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'De<PERSON>',
            'kapasitas' => 28,
            'is_active' => 1
        ],
        [
            'nama_kelas' => 'X-1',
            'tingkat' => 'X',
            'jurusan' => 'IPA',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'Siti Nurhaliza',
            'kapasitas' => 32,
            'is_active' => 1
        ],
        [
            'nama_kelas' => 'X-2',
            'tingkat' => 'X',
            'jurusan' => 'IPS',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'Andi Wijaya',
            'kapasitas' => 30,
            'is_active' => 1
        ],
        [
            'nama_kelas' => 'XI-1',
            'tingkat' => 'XI',
            'jurusan' => 'IPA',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'Maya Sari',
            'kapasitas' => 28,
            'is_active' => 1
        ],
        [
            'nama_kelas' => 'XI-2',
            'tingkat' => 'XI',
            'jurusan' => 'IPS',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'Rudi Hartono',
            'kapasitas' => 30,
            'is_active' => 1
        ],
        [
            'nama_kelas' => 'XII-1',
            'tingkat' => 'XII',
            'jurusan' => 'IPA',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'Lina Marlina',
            'kapasitas' => 25,
            'is_active' => 1
        ],
        [
            'nama_kelas' => 'XII-2',
            'tingkat' => 'XII',
            'jurusan' => 'IPS',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'Agus Salim',
            'kapasitas' => 27,
            'is_active' => 1
        ],
        [
            'nama_kelas' => 'KPA',
            'tingkat' => 'KPA',
            'jurusan' => 'Umum',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'Dewi Sartika',
            'kapasitas' => 20,
            'is_active' => 1
        ]
    ];
    
    echo "<h1>🏫 Adding Sample Kelas Data</h1>";
    echo "<div style='font-family: Arial, sans-serif; margin: 20px;'>";
    
    // Check if kelas table exists and has data
    $existingKelas = $db->fetchAll("SELECT COUNT(*) as count FROM kelas");
    $kelasCount = $existingKelas[0]['count'] ?? 0;
    
    echo "<p><strong>Current kelas count:</strong> $kelasCount</p>";
    
    if ($kelasCount > 0) {
        echo "<p style='color: orange;'>⚠️ Kelas data already exists. Do you want to add more sample data?</p>";
        echo "<p><a href='?force=1' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Yes, Add Sample Data</a></p>";
        
        if (!isset($_GET['force'])) {
            echo "<p><a href='debug_kelas.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Go to Debug Page</a></p>";
            echo "</div>";
            exit;
        }
    }
    
    // Insert sample data
    $insertedCount = 0;
    foreach ($sampleKelas as $kelas) {
        try {
            $result = $db->execute("
                INSERT INTO kelas (nama_kelas, tingkat, jurusan, tahun_pelajaran, wali_kelas, kapasitas, is_active, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
            ", [
                $kelas['nama_kelas'],
                $kelas['tingkat'],
                $kelas['jurusan'],
                $kelas['tahun_pelajaran'],
                $kelas['wali_kelas'],
                $kelas['kapasitas'],
                $kelas['is_active']
            ]);
            
            if ($result) {
                echo "<p style='color: green;'>✅ Added: {$kelas['nama_kelas']} ({$kelas['tingkat']}) - Wali: {$kelas['wali_kelas']}</p>";
                $insertedCount++;
            } else {
                echo "<p style='color: red;'>❌ Failed to add: {$kelas['nama_kelas']}</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error adding {$kelas['nama_kelas']}: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<hr>";
    echo "<h2>📊 Summary</h2>";
    echo "<p><strong>Successfully added:</strong> $insertedCount kelas</p>";
    
    // Show final count
    $finalKelas = $db->fetchAll("SELECT COUNT(*) as count FROM kelas");
    $finalCount = $finalKelas[0]['count'] ?? 0;
    echo "<p><strong>Total kelas in database:</strong> $finalCount</p>";
    
    echo "<hr>";
    echo "<h2>🔧 Next Steps</h2>";
    echo "<p>
        <a href='debug_kelas.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>
            🔍 Debug Kelas Access
        </a>
        <a href='public/kelas' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>
            🏫 Go to Kelas Management
        </a>
    </p>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h1 style='color: red;'>❌ Database Error</h1>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database connection and table structure.</p>";
}
?>
