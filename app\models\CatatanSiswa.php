<?php
require_once __DIR__ . '/Database.php';

class CatatanSiswa {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Get all catatan for a specific student
     */
    public function getBySiswaId($siswaId) {
        try {
            $result = $this->db->fetchAll("
                SELECT
                    c.*,
                    COALESCE(c.judul_catatan, c.judul, 'Catatan Siswa') as judul,
                    COALESCE(c.isi_catatan, c.catatan, '') as catatan,
                    COALESCE(c.tanggal_catatan, c.tanggal, CURDATE()) as tanggal,
                    k.nama_kategori,
                    k.warna_badge,
                    k.icon_class,
                    COALESCE(u.username, u.nama_lengkap, 'System') as created_by_name
                FROM catatan_siswa c
                LEFT JOIN kategori_catatan k ON c.jenis_catatan = k.kode_kategori
                LEFT JOIN users u ON c.created_by = u.id
                WHERE c.siswa_id = ?
                ORDER BY c.tanggal_catatan DESC, c.created_at DESC
            ", [$siswaId]);

            // Ensure required fields are available
            foreach ($result as &$catatan) {
                if (empty($catatan['judul'])) {
                    $catatan['judul'] = $catatan['judul_catatan'] ?? 'Catatan Siswa';
                }
                if (empty($catatan['catatan'])) {
                    $catatan['catatan'] = $catatan['isi_catatan'] ?? '';
                }
                if (empty($catatan['tanggal'])) {
                    $catatan['tanggal'] = $catatan['tanggal_catatan'] ?? date('Y-m-d');
                }
            }

            return $result;
        } catch (Exception $e) {
            error_log("Error getting catatan: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get catatan by category for a student
     */
    public function getBySiswaIdAndCategory($siswaId, $jenisCatatan) {
        try {
            return $this->db->fetchAll("
                SELECT c.*, k.nama_kategori, k.warna_badge, k.icon_class, u.username as created_by_name
                FROM catatan_siswa c
                LEFT JOIN kategori_catatan k ON c.jenis_catatan = k.kode_kategori
                LEFT JOIN users u ON c.created_by = u.id
                WHERE c.siswa_id = ? AND c.jenis_catatan = ?
                ORDER BY c.tanggal_catatan DESC, c.created_at DESC
            ", [$siswaId, $jenisCatatan]);
        } catch (Exception $e) {
            error_log("Error getting catatan by category: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get catatan grouped by category
     */
    public function getGroupedBySiswaId($siswaId) {
        $allCatatan = $this->getBySiswaId($siswaId);
        $grouped = [
            'wali_kelas' => [],
            'pamong' => [],
            'bk' => [],
            'akademik' => [],
            'prestasi' => [],
            'pelanggaran' => []
        ];

        foreach ($allCatatan as $catatan) {
            $jenis = $catatan['jenis_catatan'];

            // Map jenis_catatan to categories
            if (strpos($jenis, 'pamong_') === 0 || $jenis === 'pamong') {
                $grouped['pamong'][] = $catatan;
            } elseif (strpos($jenis, 'wali_') === 0 || $jenis === 'wali_kelas') {
                $grouped['wali_kelas'][] = $catatan;
            } elseif (strpos($jenis, 'bk_') === 0 || $jenis === 'bk') {
                $grouped['bk'][] = $catatan;
            } elseif ($jenis === 'akademik') {
                $grouped['akademik'][] = $catatan;
            } elseif ($jenis === 'prestasi') {
                $grouped['prestasi'][] = $catatan;
            } elseif ($jenis === 'pelanggaran') {
                $grouped['pelanggaran'][] = $catatan;
            } else {
                // Default to appropriate category based on content
                $grouped['wali_kelas'][] = $catatan;
            }
        }

        return $grouped;
    }
    
    /**
     * Create new catatan
     */
    public function create($data) {
        try {
            $sql = "INSERT INTO catatan_siswa (
                siswa_id, jenis_catatan, judul_catatan, isi_catatan, 
                tanggal_catatan, tingkat_prioritas, status_catatan, 
                tindak_lanjut, tanggal_tindak_lanjut, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $params = [
                $data['siswa_id'],
                $data['jenis_catatan'],
                $data['judul_catatan'],
                $data['isi_catatan'],
                $data['tanggal_catatan'],
                $data['tingkat_prioritas'] ?? 'sedang',
                $data['status_catatan'] ?? 'aktif',
                $data['tindak_lanjut'] ?? null,
                $data['tanggal_tindak_lanjut'] ?? null,
                $data['created_by']
            ];
            
            $this->db->query($sql, $params);
            return $this->db->lastInsertId();
        } catch (Exception $e) {
            error_log("Error creating catatan: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update catatan
     */
    public function update($id, $data) {
        try {
            $sql = "UPDATE catatan_siswa SET 
                judul_catatan = ?, isi_catatan = ?, tanggal_catatan = ?,
                tingkat_prioritas = ?, status_catatan = ?, tindak_lanjut = ?,
                tanggal_tindak_lanjut = ?, updated_by = ?
                WHERE id = ?";
            
            $params = [
                $data['judul_catatan'],
                $data['isi_catatan'],
                $data['tanggal_catatan'],
                $data['tingkat_prioritas'],
                $data['status_catatan'],
                $data['tindak_lanjut'],
                $data['tanggal_tindak_lanjut'],
                $data['updated_by'],
                $id
            ];
            
            $this->db->query($sql, $params);
            return true;
        } catch (Exception $e) {
            error_log("Error updating catatan: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete catatan
     */
    public function delete($id) {
        try {
            $this->db->query("DELETE FROM catatan_siswa WHERE id = ?", [$id]);
            return true;
        } catch (Exception $e) {
            error_log("Error deleting catatan: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all categories
     */
    public function getCategories() {
        try {
            return $this->db->fetchAll("
                SELECT * FROM kategori_catatan 
                WHERE is_active = 1 
                ORDER BY kode_kategori
            ");
        } catch (Exception $e) {
            error_log("Error getting categories: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get categories grouped by type
     */
    public function getCategoriesGrouped() {
        $categories = $this->getCategories();
        $grouped = [
            'pamong' => [],
            'wali_kelas' => [],
            'bk' => []
        ];
        
        foreach ($categories as $category) {
            $kode = $category['kode_kategori'];
            
            if (strpos($kode, 'pamong_') === 0) {
                $grouped['pamong'][] = $category;
            } elseif (strpos($kode, 'wali_') === 0) {
                $grouped['wali_kelas'][] = $category;
            } elseif (strpos($kode, 'bk_') === 0) {
                $grouped['bk'][] = $category;
            }
        }
        
        return $grouped;
    }
    
    /**
     * Get templates by category
     */
    public function getTemplatesByCategory($jenisCatatan) {
        try {
            return $this->db->fetchAll("
                SELECT * FROM template_catatan 
                WHERE jenis_catatan = ? AND is_active = 1
                ORDER BY nama_template
            ", [$jenisCatatan]);
        } catch (Exception $e) {
            error_log("Error getting templates: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get statistics for dashboard
     */
    public function getStatistics($siswaId = null) {
        try {
            $whereClause = $siswaId ? "WHERE siswa_id = ?" : "";
            $params = $siswaId ? [$siswaId] : [];
            
            return $this->db->fetchAll("
                SELECT 
                    jenis_catatan,
                    COUNT(*) as total,
                    SUM(CASE WHEN status_catatan = 'aktif' THEN 1 ELSE 0 END) as aktif,
                    SUM(CASE WHEN tingkat_prioritas = 'urgent' THEN 1 ELSE 0 END) as urgent
                FROM catatan_siswa 
                $whereClause
                GROUP BY jenis_catatan
            ", $params);
        } catch (Exception $e) {
            error_log("Error getting statistics: " . $e->getMessage());
            return [];
        }
    }
}
?>
