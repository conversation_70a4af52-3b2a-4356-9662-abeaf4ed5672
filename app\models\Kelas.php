<?php
require_once __DIR__ . '/Database.php';
require_once __DIR__ . '/../helpers/Security.php';

class Kelas {
    private $db;

    public function __construct() {
        $this->db = new Database();
    }

    /**
     * Get all active classes
     */
    public function getAll() {
        try {
            return $this->db->fetchAll("
                SELECT k.id as id_kelas, k.nama_kelas, k.tingkat, k.tahun_pelajaran, k.wali_kelas, k.kapas<PERSON>, k.is_active, k.created_at,
                       kr.nama_kurikulum, kr.kode_kurikulum, k.kurikulum_id
                FROM kelas k
                LEFT JOIN kurikulum kr ON k.kurikulum_id = kr.id_kurikulum
                WHERE k.is_active = 1
                ORDER BY
                    CASE k.tingkat
                        WHEN 'KPP' THEN 1
                        WHEN 'X' THEN 2
                        WHEN 'XI' THEN 3
                        WHEN 'XII' THEN 4
                        WHEN 'KPA' THEN 5
                        ELSE 6
                    END,
                    k.nama_kelas
            ");
        } catch (Exception $e) {
            error_log("Error in Kelas::getAll(): " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get class by ID
     */
    public function getById($id) {
        try {
            return $this->db->fetch("
                SELECT k.id as id_kelas, k.nama_kelas, k.tingkat, k.tahun_pelajaran, k.wali_kelas, k.kapasitas, k.is_active, k.created_at,
                       kr.nama_kurikulum, kr.kode_kurikulum, k.kurikulum_id
                FROM kelas k
                LEFT JOIN kurikulum kr ON k.kurikulum_id = kr.id_kurikulum
                WHERE k.id = ?
            ", [$id]);
        } catch (Exception $e) {
            error_log("Error in Kelas::getById(): " . $e->getMessage());
            return null;
        }
    }

    /**
     * Create new class
     */
    public function create($nama_kelas) {
        try {
            $this->db->query("INSERT INTO kelas (nama_kelas, tingkat, tahun_pelajaran) VALUES (?, 10, '2024/2025')", [$nama_kelas]);
            return $this->db->lastInsertId();
        } catch (Exception $e) {
            error_log("Error in Kelas::create(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Create new class with full data
     */
    public function createKelas($data) {
        try {
            $sql = "INSERT INTO kelas (nama_kelas, tingkat, kurikulum_id, tahun_pelajaran, wali_kelas, kapasitas, created_by, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";

            $params = [
                $data['nama_kelas'],
                $data['tingkat'],
                $data['kurikulum_id'] ?? null,
                $data['tahun_pelajaran'],
                $data['wali_kelas'] ?? null,
                $data['kapasitas'] ?? 30,
                $data['created_by'] ?? 1
            ];

            $this->db->query($sql, $params);

            $kelasId = $this->db->lastInsertId();

            // Log activity
            Security::logSecurityEvent('kelas_created', [
                'kelas_id' => $kelasId,
                'nama_kelas' => $data['nama_kelas'],
                'created_by' => $data['created_by'] ?? 1
            ]);

            return $kelasId;
        } catch (Exception $e) {
            error_log("Error in Kelas::createKelas(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Update class
     */
    public function updateKelas($id, $data) {
        try {
            $sql = "UPDATE kelas SET
                    nama_kelas = ?,
                    tingkat = ?,
                    kurikulum_id = ?,
                    tahun_pelajaran = ?,
                    wali_kelas = ?,
                    kapasitas = ?,
                    updated_by = ?,
                    updated_at = NOW()
                    WHERE id = ?";

            $params = [
                $data['nama_kelas'],
                $data['tingkat'],
                $data['kurikulum_id'] ?? null,
                $data['tahun_pelajaran'],
                $data['wali_kelas'] ?? null,
                $data['kapasitas'] ?? 30,
                $data['updated_by'] ?? 1,
                $id
            ];

            $this->db->query($sql, $params);

            // Log activity
            Security::logSecurityEvent('kelas_updated', [
                'kelas_id' => $id,
                'nama_kelas' => $data['nama_kelas'],
                'updated_by' => $data['updated_by'] ?? 1
            ]);

            return true;
        } catch (Exception $e) {
            error_log("Error in Kelas::updateKelas(): " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete class (soft delete)
     */
    public function deleteKelas($id) {
        try {
            // Check if there are students in this class
            $studentCount = $this->db->fetchOne("SELECT COUNT(*) as count FROM siswa WHERE kelas_id = ?", [$id]);

            if ($studentCount && $studentCount['count'] > 0) {
                throw new Exception("Tidak dapat menghapus kelas yang masih memiliki siswa. Terdapat {$studentCount['count']} siswa di kelas ini.");
            }

            // Soft delete - set is_active to 0
            $result = $this->db->execute("UPDATE kelas SET is_active = 0, updated_at = NOW() WHERE id = ?", [$id]);

            if ($result) {
                // Log activity if Security class has this method
                if (method_exists('Security', 'logSecurityEvent')) {
                    Security::logSecurityEvent('kelas_deleted', [
                        'kelas_id' => $id,
                        'deleted_by' => $_SESSION['user_id'] ?? 1
                    ]);
                }
                return true;
            } else {
                throw new Exception("Gagal menghapus kelas dari database.");
            }
        } catch (Exception $e) {
            error_log("Error in Kelas::deleteKelas(): " . $e->getMessage());
            throw $e; // Re-throw exception so controller can handle it
        }
    }

    /**
     * Get class statistics
     */
    public function getStatistics() {
        try {
            $stats = [];

            // Total classes
            $total = $this->db->fetch("SELECT COUNT(*) as count FROM kelas WHERE is_active = 1");
            $stats['total_kelas'] = $total['count'] ?? 0;

            // Classes by level
            $byLevel = $this->db->fetchAll("
                SELECT tingkat, COUNT(*) as count
                FROM kelas
                WHERE is_active = 1
                GROUP BY tingkat
                ORDER BY tingkat
            ");
            $stats['by_level'] = $byLevel;

            // Classes with students count
            $withStudents = $this->db->fetchAll("
                SELECT k.nama_kelas, k.tingkat, COUNT(s.id_siswa) as student_count, k.kapasitas
                FROM kelas k
                LEFT JOIN siswa s ON k.id = s.kelas_id AND s.status_siswa = 'aktif'
                WHERE k.is_active = 1
                GROUP BY k.id, k.nama_kelas, k.tingkat, k.kapasitas
                ORDER BY k.tingkat, k.nama_kelas
            ");
            $stats['with_students'] = $withStudents;

            return $stats;
        } catch (Exception $e) {
            error_log("Error in Kelas::getStatistics(): " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get classes for dropdown/select options
     */
    public function getForSelect() {
        try {
            return $this->db->fetchAll("
                SELECT id as value, CONCAT(nama_kelas, ' (', tingkat, ')') as label
                FROM kelas
                WHERE is_active = 1
                ORDER BY
                    CASE tingkat
                        WHEN 'KPP' THEN 1
                        WHEN 'X' THEN 2
                        WHEN 'XI' THEN 3
                        WHEN 'XII' THEN 4
                        WHEN 'KPA' THEN 5
                        ELSE 6
                    END,
                    nama_kelas
            ");
        } catch (Exception $e) {
            error_log("Error in Kelas::getForSelect(): " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get classes by tingkat (for pamong role filtering)
     */
    public function getByTingkat($allowedTingkat) {
        if (empty($allowedTingkat)) {
            return [];
        }

        try {
            $placeholders = str_repeat('?,', count($allowedTingkat) - 1) . '?';

            return $this->db->fetchAll("
                SELECT k.id as id_kelas, k.nama_kelas, k.tingkat, k.tahun_pelajaran, k.wali_kelas, k.kapasitas, k.is_active, k.created_at,
                       kr.nama_kurikulum, kr.kode_kurikulum, k.kurikulum_id
                FROM kelas k
                LEFT JOIN kurikulum kr ON k.kurikulum_id = kr.id_kurikulum
                WHERE k.tingkat IN ($placeholders)
                AND k.is_active = 1
                ORDER BY
                    CASE k.tingkat
                        WHEN 'KPP' THEN 1
                        WHEN 'X' THEN 2
                        WHEN 'XI' THEN 3
                        WHEN 'XII' THEN 4
                        WHEN 'KPA' THEN 5
                        ELSE 6
                    END,
                    k.nama_kelas
            ", $allowedTingkat);
        } catch (Exception $e) {
            error_log("Error in getByTingkat: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get classes by wali kelas name
     */
    public function getByWaliKelas($waliKelasName) {
        try {
            return $this->db->fetchAll("
                SELECT k.id as id_kelas, k.nama_kelas, k.tingkat, k.tahun_pelajaran, k.wali_kelas, k.kapasitas, k.is_active, k.created_at,
                       kr.nama_kurikulum, kr.kode_kurikulum, k.kurikulum_id
                FROM kelas k
                LEFT JOIN kurikulum kr ON k.kurikulum_id = kr.id_kurikulum
                WHERE k.wali_kelas = ?
                AND k.is_active = 1
                ORDER BY
                    CASE k.tingkat
                        WHEN 'KPP' THEN 1
                        WHEN 'X' THEN 2
                        WHEN 'XI' THEN 3
                        WHEN 'XII' THEN 4
                        WHEN 'KPA' THEN 5
                        ELSE 6
                    END,
                    k.nama_kelas
            ", [$waliKelasName]);
        } catch (Exception $e) {
            error_log("Error in getByWaliKelas: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get active kurikulum for dropdown
     */
    public function getActiveKurikulum() {
        try {
            return $this->db->fetchAll("
                SELECT id_kurikulum, nama_kurikulum, kode_kurikulum
                FROM kurikulum
                WHERE is_active = 1
                ORDER BY nama_kurikulum ASC
            ");
        } catch (Exception $e) {
            error_log("Error in getActiveKurikulum: " . $e->getMessage());
            return [];
        }
    }
}
?>