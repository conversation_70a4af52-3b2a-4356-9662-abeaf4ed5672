<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Elegant Alerts</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="bi bi-palette"></i>
                    Demo Elegant Alert System
                </h1>
                <p class="text-muted mb-4">
                    Sistem notifikasi yang elegan dan modern untuk pengalaman pengguna yang lebih baik.
                </p>

                <!-- Demo Buttons -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-danger w-100" onclick="showErrorDemo()">
                            <i class="bi bi-exclamation-triangle"></i>
                            Show Error Alert
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-success w-100" onclick="showSuccessDemo()">
                            <i class="bi bi-check-circle"></i>
                            Show Success Alert
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-warning w-100" onclick="showWarningDemo()">
                            <i class="bi bi-exclamation-triangle"></i>
                            Show Warning Alert
                        </button>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button class="btn btn-info w-100" onclick="showInfoDemo()">
                            <i class="bi bi-info-circle"></i>
                            Show Info Alert
                        </button>
                    </div>
                </div>

                <!-- Features -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-star"></i>
                                    Fitur Utama
                                </h5>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="bi bi-check-circle text-success me-2"></i>
                                        Desain modern dengan gradient background
                                    </li>
                                    <li class="mb-2">
                                        <i class="bi bi-check-circle text-success me-2"></i>
                                        Animasi smooth slide in/out
                                    </li>
                                    <li class="mb-2">
                                        <i class="bi bi-check-circle text-success me-2"></i>
                                        Auto-dismiss setelah 8 detik
                                    </li>
                                    <li class="mb-2">
                                        <i class="bi bi-check-circle text-success me-2"></i>
                                        Progress bar untuk countdown
                                    </li>
                                    <li class="mb-2">
                                        <i class="bi bi-check-circle text-success me-2"></i>
                                        Action buttons yang customizable
                                    </li>
                                    <li class="mb-2">
                                        <i class="bi bi-check-circle text-success me-2"></i>
                                        Responsive design untuk mobile
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-palette"></i>
                                    Jenis Alert
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-6 mb-3">
                                        <div class="d-flex align-items-center">
                                            <div class="alert-demo-icon bg-danger text-white me-2">
                                                <i class="bi bi-exclamation-triangle-fill"></i>
                                            </div>
                                            <div>
                                                <strong>Error</strong><br>
                                                <small class="text-muted">Untuk kesalahan sistem</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <div class="d-flex align-items-center">
                                            <div class="alert-demo-icon bg-success text-white me-2">
                                                <i class="bi bi-check-circle-fill"></i>
                                            </div>
                                            <div>
                                                <strong>Success</strong><br>
                                                <small class="text-muted">Untuk operasi berhasil</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <div class="d-flex align-items-center">
                                            <div class="alert-demo-icon bg-warning text-dark me-2">
                                                <i class="bi bi-exclamation-triangle-fill"></i>
                                            </div>
                                            <div>
                                                <strong>Warning</strong><br>
                                                <small class="text-muted">Untuk peringatan</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6 mb-3">
                                        <div class="d-flex align-items-center">
                                            <div class="alert-demo-icon bg-info text-white me-2">
                                                <i class="bi bi-info-circle-fill"></i>
                                            </div>
                                            <div>
                                                <strong>Info</strong><br>
                                                <small class="text-muted">Untuk informasi umum</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php
    require_once __DIR__ . '/../components/elegant-alerts.php';
    echo getAlertStyles();
    echo getAlertScripts();
    ?>

    <style>
    .alert-demo-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
    }
    </style>

    <script>
    function showErrorDemo() {
        showNotification(
            'error',
            'Oops! Terjadi Kesalahan',
            'Tidak dapat menghapus kelas yang masih memiliki siswa. Terdapat 15 siswa di kelas ini.',
            [
                {
                    class: 'btn-retry',
                    icon: 'bi-arrow-clockwise',
                    text: 'Coba Lagi',
                    onclick: 'location.reload()'
                }
            ]
        );
    }

    function showSuccessDemo() {
        showNotification(
            'success',
            'Berhasil!',
            'Kelas "XII IPA 1" berhasil ditambahkan ke sistem. Sekarang Anda dapat menambahkan siswa ke kelas ini.',
            []
        );
    }

    function showWarningDemo() {
        showNotification(
            'warning',
            'Perhatian!',
            'Kapasitas kelas hampir penuh. Hanya tersisa 3 slot untuk siswa baru di kelas ini.',
            [
                {
                    class: 'btn-action',
                    icon: 'bi-plus-circle',
                    text: 'Tambah Siswa',
                    onclick: 'alert("Navigasi ke halaman tambah siswa")'
                }
            ]
        );
    }

    function showInfoDemo() {
        showNotification(
            'info',
            'Informasi',
            'Sistem akan melakukan maintenance pada hari Minggu, 15 Januari 2024 pukul 02:00 - 04:00 WIB.',
            [
                {
                    class: 'btn-action',
                    icon: 'bi-calendar',
                    text: 'Lihat Jadwal',
                    onclick: 'alert("Menampilkan jadwal maintenance")'
                }
            ]
        );
    }
    </script>
</body>
</html>
