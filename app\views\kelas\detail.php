<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Kelas Information Card -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">
                        <i class="bi bi-building"></i> Detail Kelas: <?= htmlspecialchars($kelas['nama_kelas']) ?>
                    </h4>
                    <div class="btn-group">
                        <a href="/siswa-app/public/kelas/edit/<?= $kelas['id_kelas'] ?>" class="btn btn-warning">
                            <i class="bi bi-pencil"></i> Edit
                        </a>
                        <a href="/siswa-app/public/kelas" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> Kembali
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong><i class="bi bi-tag"></i> Nama <PERSON>:</strong></td>
                                    <td><?= htmlspecialchars($kelas['nama_kelas']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong><i class="bi bi-layers"></i> Tingkat:</strong></td>
                                    <td>
                                        <?php
                                        $tingkat = $kelas['tingkat'] ?? '';

                                        // Determine badge class and label
                                        switch($tingkat) {
                                            case 'KPP':
                                                $badgeClass = 'bg-info';
                                                $tingkatLabel = 'KPP (Kelas Persiapan Program)';
                                                break;
                                            case 'X':
                                                $badgeClass = 'bg-success';
                                                $tingkatLabel = 'Kelas X (Kelas 10)';
                                                break;
                                            case 'XI':
                                                $badgeClass = 'bg-warning';
                                                $tingkatLabel = 'Kelas XI (Kelas 11)';
                                                break;
                                            case 'XII':
                                                $badgeClass = 'bg-danger';
                                                $tingkatLabel = 'Kelas XII (Kelas 12)';
                                                break;
                                            case 'KPA':
                                                $badgeClass = 'bg-purple';
                                                $tingkatLabel = 'KPA (Kelas Program Akselerasi)';
                                                break;
                                            default:
                                                $badgeClass = 'bg-secondary';
                                                $tingkatLabel = $tingkat;
                                        }
                                        ?>
                                        <span class="badge <?= $badgeClass ?>"><?= htmlspecialchars($tingkatLabel) ?></span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong><i class="bi bi-book-half"></i> Kurikulum:</strong></td>
                                    <td>
                                        <?php if (!empty($kelas['nama_kurikulum'])): ?>
                                            <div class="d-flex align-items-center">
                                                <div class="kurikulum-icon mr-2">
                                                    <i class="bi bi-book-half text-primary"></i>
                                                </div>
                                                <div>
                                                    <span class="badge bg-primary text-white">
                                                        <?= htmlspecialchars($kelas['nama_kurikulum']) ?>
                                                    </span>
                                                    <br>
                                                    <small class="text-muted">
                                                        Kode: <?= htmlspecialchars($kelas['kode_kurikulum']) ?>
                                                    </small>
                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">Belum ada kurikulum</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong><i class="bi bi-calendar"></i> Tahun Pelajaran:</strong></td>
                                    <td><?= htmlspecialchars($kelas['tahun_pelajaran']) ?></td>
                                </tr>
                                <tr>
                                    <td><strong><i class="bi bi-person"></i> Wali Kelas:</strong></td>
                                    <td><?= htmlspecialchars($kelas['wali_kelas'] ?? '-') ?></td>
                                </tr>
                                <tr>
                                    <td><strong><i class="bi bi-people"></i> Kapasitas:</strong></td>
                                    <td>
                                        <span class="badge bg-info"><?= htmlspecialchars($kelas['kapasitas'] ?? '30') ?> siswa</span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Students List Card -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-people"></i> Daftar Siswa 
                        <span class="badge bg-primary"><?= count($siswa_list ?? []) ?> siswa</span>
                    </h5>
                    <a href="/siswa-app/public/siswa/create?kelas_id=<?= $kelas['id_kelas'] ?>" class="btn btn-success btn-sm">
                        <i class="bi bi-person-plus"></i> Tambah Siswa
                    </a>
                </div>
                <div class="card-body">
                    <?php if (empty($siswa_list)): ?>
                        <div class="alert alert-info text-center">
                            <i class="bi bi-info-circle"></i>
                            Belum ada siswa di kelas ini. 
                            <a href="/siswa-app/public/siswa/create?kelas_id=<?= $kelas['id_kelas'] ?>">Tambah siswa pertama</a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>No</th>
                                        <th>NIS</th>
                                        <th>Nama Lengkap</th>
                                        <th>Jenis Kelamin</th>
                                        <th>Status</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($siswa_list as $index => $siswa): ?>
                                    <tr>
                                        <td><?= $index + 1 ?></td>
                                        <td>
                                            <strong><?= htmlspecialchars($siswa['nis']) ?></strong>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php if (!empty($siswa['foto'])): ?>
                                                    <img src="/siswa-app/uploads/<?= htmlspecialchars($siswa['foto']) ?>" 
                                                         alt="Foto" class="rounded-circle me-2" width="32" height="32">
                                                <?php else: ?>
                                                    <div class="bg-secondary rounded-circle me-2 d-flex align-items-center justify-content-center" 
                                                         style="width: 32px; height: 32px;">
                                                        <i class="bi bi-person text-white"></i>
                                                    </div>
                                                <?php endif; ?>
                                                <?= htmlspecialchars($siswa['nama_lengkap']) ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if ($siswa['jenis_kelamin'] == 'L'): ?>
                                                <span class="badge bg-primary">Laki-laki</span>
                                            <?php else: ?>
                                                <span class="badge bg-pink">Perempuan</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                            $status = $siswa['status_siswa'] ?? 'aktif';

                                            // Determine badge class for status
                                            switch($status) {
                                                case 'aktif':
                                                    $badgeClass = 'bg-success';
                                                    break;
                                                case 'lulus':
                                                    $badgeClass = 'bg-info';
                                                    break;
                                                case 'mutasi':
                                                    $badgeClass = 'bg-warning';
                                                    break;
                                                case 'dropout':
                                                    $badgeClass = 'bg-danger';
                                                    break;
                                                default:
                                                    $badgeClass = 'bg-secondary';
                                            }
                                            ?>
                                            <span class="badge <?= $badgeClass ?>"><?= ucfirst($status) ?></span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="/siswa-app/public/siswa/detail/<?= $siswa['id_siswa'] ?>" 
                                                   class="btn btn-outline-info" title="Detail">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="/siswa-app/public/siswa/edit/<?= $siswa['id_siswa'] ?>" 
                                                   class="btn btn-outline-warning" title="Edit">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.bg-pink {
    background-color: #e91e63 !important;
}
.bg-purple {
    background-color: #6f42c1 !important;
}

/* Kurikulum Icon Styling */
.kurikulum-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 123, 255, 0.1);
    border-radius: 50%;
    flex-shrink: 0;
}

.kurikulum-icon i {
    font-size: 1.1rem;
}

/* Badge Enhancements */
.badge {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
}

/* Table Enhancements */
.table-borderless td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
}

.table-borderless td strong {
    color: #495057;
    font-weight: 600;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .kurikulum-icon {
        width: 28px;
        height: 28px;
    }

    .kurikulum-icon i {
        font-size: 1rem;
    }

    .badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }
}
</style>
