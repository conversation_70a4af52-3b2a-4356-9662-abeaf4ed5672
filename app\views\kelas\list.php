<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="bi bi-building text-primary"></i>
                        Manajemen Kelas
                    </h1>
                    <p class="text-muted mb-0">
                        Kelola data kelas dan informasi akademik
                        <?php if (isset($user_role)): ?>
                        <span class="badge bg-secondary ms-2">
                            <?php
                            switch($user_role) {
                                case 'admin': echo 'Admin - <PERSON><PERSON><PERSON>'; break;
                                case 'pamong': echo '<PERSON>ong - <PERSON>ks<PERSON>'; break;
                                case 'wali_kelas': echo 'Wali Kelas - Kelas Sendiri'; break;
                                case 'staff': echo 'Staff - Lihat Saja'; break;
                                default: echo ucfirst($user_role);
                            }
                            ?>
                        </span>
                        <?php endif; ?>
                    </p>
                </div>
                <div>
                    <?php if (isset($can_manage) && $can_manage): ?>
                    <a href="/siswa-app/public/kelas/create" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i>
                        Tambah Kelas Baru
                    </a>
                    <?php else: ?>
                    <span class="badge bg-info">
                        <i class="bi bi-eye"></i>
                        Mode Lihat Saja
                    </span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <?php if (isset($_SESSION['error'])): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i>
        <strong>Error!</strong> <?= htmlspecialchars($_SESSION['error']) ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <?php unset($_SESSION['error']); endif; ?>

    <?php if (isset($_SESSION['success'])): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i>
        <strong>Berhasil!</strong> <?= htmlspecialchars($_SESSION['success']) ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <?php unset($_SESSION['success']); endif; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Kelas
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= count($kelas) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-building fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Kelas Aktif
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= count(array_filter($kelas, function($k) { return $k['is_active'] == 1; })) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Tingkat Tersedia
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= count(array_unique(array_column($kelas, 'tingkat'))) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-layers fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Tahun Pelajaran
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= !empty($kelas) ? $kelas[0]['tahun_pelajaran'] : '2024/2025' ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-list"></i>
                        Daftar Kelas
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($kelas)): ?>
                        <div class="empty-state text-center py-5">
                            <i class="bi bi-building display-1 text-muted mb-3"></i>
                            <h4 class="text-muted">Belum Ada Kelas</h4>
                            <p class="text-muted mb-4">Mulai dengan menambahkan kelas pertama untuk sistem akademik Anda</p>
                            <a href="/siswa-app/public/kelas/create" class="btn btn-primary">
                                <i class="bi bi-plus-circle"></i>
                                Tambah Kelas Pertama
                            </a>
                        </div>
                    <?php else: ?>
                        <!-- Search and Filter -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-search"></i>
                                    </span>
                                    <input type="text" class="form-control" id="searchInput"
                                           placeholder="Cari nama kelas, tingkat, atau wali kelas...">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-control" id="filterTingkat">
                                    <option value="">Semua Tingkat</option>
                                    <option value="KPP">KPP</option>
                                    <option value="X">Kelas X</option>
                                    <option value="XI">Kelas XI</option>
                                    <option value="XII">Kelas XII</option>
                                    <option value="KPA">KPA</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-control" id="filterKurikulum">
                                    <option value="">Semua Kurikulum</option>
                                    <option value="MERDEKA">Kurikulum Merdeka</option>
                                    <option value="SEMINARI">Kurikulum Seminari</option>
                                </select>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover" id="kelasTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th width="8%">
                                            <i class="bi bi-hash"></i>
                                            ID
                                        </th>
                                        <th width="20%">
                                            <i class="bi bi-tag"></i>
                                            Nama Kelas
                                        </th>
                                        <th width="15%">
                                            <i class="bi bi-layers"></i>
                                            Tingkat
                                        </th>
                                        <th width="12%">
                                            <i class="bi bi-book-half"></i>
                                            Kurikulum
                                        </th>
                                        <th width="15%">
                                            <i class="bi bi-calendar"></i>
                                            Tahun Pelajaran
                                        </th>
                                        <th width="15%">
                                            <i class="bi bi-person"></i>
                                            Wali Kelas
                                        </th>
                                        <th width="15%" class="text-center">
                                            <i class="bi bi-gear"></i>
                                            Aksi
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($kelas as $k): ?>
                                    <tr class="kelas-row"
                                        data-tingkat="<?= htmlspecialchars($k['tingkat'] ?? '') ?>"
                                        data-kurikulum="<?= htmlspecialchars($k['kode_kurikulum'] ?? '') ?>"
                                        data-search="<?= htmlspecialchars(strtolower($k['nama_kelas'] . ' ' . $k['tingkat'] . ' ' . $k['wali_kelas'] . ' ' . $k['nama_kurikulum'])) ?>">
                                        <td>
                                            <span class="badge bg-light text-dark">
                                                <?= htmlspecialchars($k['id_kelas'] ?? '') ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="kelas-icon mr-2">
                                                    <i class="bi bi-building text-primary"></i>
                                                </div>
                                                <div>
                                                    <strong class="kelas-name">
                                                        <?= htmlspecialchars($k['nama_kelas'] ?? '') ?>
                                                    </strong>
                                                    <?php if (!empty($k['kapasitas'])): ?>
                                                    <br><small class="text-muted">
                                                        <i class="bi bi-people"></i>
                                                        Kapasitas: <?= htmlspecialchars($k['kapasitas']) ?> siswa
                                                    </small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <?php
                                            $tingkat = $k['tingkat'] ?? '';
                                            // Determine badge class and label
                                            switch($tingkat) {
                                                case 'KPP':
                                                    $badgeClass = 'bg-info text-white';
                                                    $tingkatLabel = 'KPP';
                                                    $tingkatDesc = 'Kelas Persiapan Pertama';
                                                    break;
                                                case 'X':
                                                    $badgeClass = 'bg-success text-white';
                                                    $tingkatLabel = 'X';
                                                    $tingkatDesc = 'Kelas 10';
                                                    break;
                                                case 'XI':
                                                    $badgeClass = 'bg-warning text-dark';
                                                    $tingkatLabel = 'XI';
                                                    $tingkatDesc = 'Kelas 11';
                                                    break;
                                                case 'XII':
                                                    $badgeClass = 'bg-danger text-white';
                                                    $tingkatLabel = 'XII';
                                                    $tingkatDesc = 'Kelas 12';
                                                    break;
                                                case 'KPA':
                                                    $badgeClass = 'bg-purple text-white';
                                                    $tingkatLabel = 'KPA';
                                                    $tingkatDesc = 'Kelas Persiapan Atas';
                                                    break;
                                                default:
                                                    $badgeClass = 'bg-secondary text-white';
                                                    $tingkatLabel = $tingkat ?: 'N/A';
                                                    $tingkatDesc = 'Tingkat Lainnya';
                                            }
                                            ?>
                                            <span class="badge <?= $badgeClass ?>" title="<?= $tingkatDesc ?>">
                                                <?= htmlspecialchars($tingkatLabel) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if (!empty($k['nama_kurikulum'])): ?>
                                                <div class="d-flex align-items-center">
                                                    <div class="kurikulum-icon mr-2">
                                                        <i class="bi bi-book-half text-primary"></i>
                                                    </div>
                                                    <div>
                                                        <span class="badge bg-primary text-white">
                                                            <?= htmlspecialchars($k['nama_kurikulum']) ?>
                                                        </span>
                                                        <br>
                                                        <small class="text-muted">
                                                            <?= htmlspecialchars($k['kode_kurikulum']) ?>
                                                        </small>
                                                    </div>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="text-primary font-weight-bold">
                                                <?= htmlspecialchars($k['tahun_pelajaran'] ?? '-') ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if (!empty($k['wali_kelas'])): ?>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-person-circle text-muted mr-1"></i>
                                                    <span><?= htmlspecialchars($k['wali_kelas']) ?></span>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-muted">
                                                    <i class="bi bi-person-dash"></i>
                                                    Belum ditentukan
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="/siswa-app/public/kelas/detail/<?= $k['id_kelas'] ?>"
                                                   class="btn btn-outline-info"
                                                   title="Lihat Detail"
                                                   data-toggle="tooltip">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <?php if (isset($can_manage) && $can_manage): ?>
                                                <a href="/siswa-app/public/kelas/edit/<?= $k['id_kelas'] ?>"
                                                   class="btn btn-outline-warning"
                                                   title="Edit Kelas"
                                                   data-toggle="tooltip">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <button type="button"
                                                        class="btn btn-outline-danger"
                                                        onclick="confirmDelete(<?= $k['id_kelas'] ?>, '<?= htmlspecialchars($k['nama_kelas']) ?>')"
                                                        title="Hapus Kelas"
                                                        data-toggle="tooltip">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom CSS -->
<style>
/* Statistics Cards */
.border-left-primary { border-left: 0.25rem solid #4e73df !important; }
.border-left-success { border-left: 0.25rem solid #1cc88a !important; }
.border-left-info { border-left: 0.25rem solid #36b9cc !important; }
.border-left-warning { border-left: 0.25rem solid #f6c23e !important; }
.text-gray-800 { color: #5a5c69 !important; }
.text-gray-300 { color: #dddfeb !important; }
.font-weight-bold { font-weight: 700 !important; }
.text-xs { font-size: 0.7rem; }
.fa-2x { font-size: 2em; }

/* Badge Styles */
.bg-purple {
    background-color: #6f42c1 !important;
}

.badge {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
}

.badge.bg-light {
    border: 1px solid #dee2e6;
}

.badge.bg-primary {
    background-color: #007bff !important;
}

.badge.bg-info {
    background-color: #17a2b8 !important;
}

.badge.bg-success {
    background-color: #28a745 !important;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
}

.badge.bg-danger {
    background-color: #dc3545 !important;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
}

/* Table Enhancements */
.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

.kelas-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 123, 255, 0.1);
    border-radius: 50%;
}

.kelas-name {
    font-size: 1rem;
    color: #2c3e50;
}

/* Search and Filter */
.input-group-text {
    background-color: #f8f9fa;
    border-color: #ced4da;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Empty State */
.empty-state {
    padding: 3rem 1rem;
}

.empty-state .display-1 {
    font-size: 4rem;
    opacity: 0.3;
}

/* Button Enhancements */
.btn-group-sm .btn {
    padding: 0.375rem 0.5rem;
    margin: 0 1px;
    border-radius: 0.25rem;
    transition: all 0.15s ease-in-out;
}

.btn-outline-info:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(23, 162, 184, 0.3);
}

.btn-outline-warning:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
}

.btn-outline-danger:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

/* Card Enhancements */
.card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    border-radius: 0.75rem 0.75rem 0 0 !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.125);
}

/* Responsive */
@media (max-width: 768px) {
    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }

    .btn-group-sm {
        flex-direction: column;
    }

    .btn-group-sm .btn {
        margin-bottom: 0.25rem;
    }
}

/* Animation */
.kelas-row {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* No Results */
.no-results {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}
</style>

<!-- Custom JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    if (typeof $('[data-toggle="tooltip"]').tooltip === 'function') {
        $('[data-toggle="tooltip"]').tooltip();
    }

    // Search functionality
    const searchInput = document.getElementById('searchInput');
    const filterTingkat = document.getElementById('filterTingkat');
    const filterKurikulum = document.getElementById('filterKurikulum');
    const kelasRows = document.querySelectorAll('.kelas-row');

    function filterTable() {
        const searchTerm = searchInput.value.toLowerCase();
        const selectedTingkat = filterTingkat.value;
        const selectedKurikulum = filterKurikulum.value;

        let visibleCount = 0;

        kelasRows.forEach(row => {
            const searchData = row.getAttribute('data-search');
            const tingkatData = row.getAttribute('data-tingkat');
            const kurikulumData = row.getAttribute('data-kurikulum');

            const matchesSearch = searchData.includes(searchTerm);
            const matchesTingkat = !selectedTingkat || tingkatData === selectedTingkat;
            const matchesKurikulum = !selectedKurikulum || kurikulumData === selectedKurikulum;

            if (matchesSearch && matchesTingkat && matchesKurikulum) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });

        // Show/hide no results message
        showNoResults(visibleCount === 0);
    }

    function showNoResults(show) {
        let noResultsRow = document.getElementById('noResultsRow');

        if (show && !noResultsRow) {
            const tbody = document.querySelector('#kelasTable tbody');
            noResultsRow = document.createElement('tr');
            noResultsRow.id = 'noResultsRow';
            noResultsRow.innerHTML = `
                <td colspan="7" class="no-results">
                    <i class="bi bi-search display-4 text-muted mb-3"></i>
                    <h5 class="text-muted">Tidak ada kelas yang ditemukan</h5>
                    <p class="text-muted">Coba ubah kata kunci pencarian atau filter yang digunakan</p>
                </td>
            `;
            tbody.appendChild(noResultsRow);
        } else if (!show && noResultsRow) {
            noResultsRow.remove();
        }
    }

    // Event listeners
    searchInput.addEventListener('input', filterTable);
    filterTingkat.addEventListener('change', filterTable);
    filterKurikulum.addEventListener('change', filterTable);

    // Auto-dismiss alerts
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            if (alert.classList.contains('alert-success')) {
                alert.style.transition = 'opacity 0.5s ease-out';
                alert.style.opacity = '0';
                setTimeout(() => {
                    alert.remove();
                }, 500);
            }
        });
    }, 5000);
});

// Enhanced delete confirmation
function confirmDelete(id, namaKelas) {
    console.log('confirmDelete called with:', id, namaKelas);
    const modal = `
        <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header bg-danger text-white">
                        <h5 class="modal-title">
                            <i class="bi bi-exclamation-triangle"></i>
                            Konfirmasi Hapus Kelas
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="text-center mb-3">
                            <i class="bi bi-building display-1 text-danger"></i>
                        </div>
                        <p class="text-center">
                            Apakah Anda yakin ingin menghapus kelas <strong>"${namaKelas}"</strong>?
                        </p>
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            <strong>Peringatan:</strong> Tindakan ini tidak dapat dibatalkan.
                            Semua data yang terkait dengan kelas ini akan ikut terhapus.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="bi bi-x-circle"></i> Batal
                        </button>
                        <button type="button" class="btn btn-danger" onclick="executeDelete(${id})">
                            <i class="bi bi-trash"></i> Ya, Hapus Kelas
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('deleteModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modal);

    // Show modal using Bootstrap 5
    const modalElement = document.getElementById('deleteModal');
    if (modalElement) {
        const bootstrapModal = new bootstrap.Modal(modalElement);
        bootstrapModal.show();
    } else {
        // Fallback for basic confirmation
        if (confirm(`Apakah Anda yakin ingin menghapus kelas "${namaKelas}"?`)) {
            executeDelete(id);
        }
    }
}

function executeDelete(id) {
    console.log('executeDelete called with:', id);
    // Hide modal using Bootstrap 5
    const modalElement = document.getElementById('deleteModal');
    if (modalElement) {
        const bootstrapModal = bootstrap.Modal.getInstance(modalElement);
        if (bootstrapModal) {
            bootstrapModal.hide();
        }
    }

    // Create form for POST request with CSRF token
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '/siswa-app/public/kelas/delete/' + id;

    // Add CSRF token
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = 'csrf_token';
    csrfInput.value = '<?= $csrf_token ?>';
    form.appendChild(csrfInput);

    // Add to body and submit
    document.body.appendChild(form);
    form.submit();
}
</script>