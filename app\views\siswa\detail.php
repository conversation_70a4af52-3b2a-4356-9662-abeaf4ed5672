<?php
// Check if siswa data exists
if (!isset($siswa) || empty($siswa)) {
    echo '<div class="alert alert-danger">Data siswa tidak ditemukan.</div>';
    return;
}

// Load berkas model safely
try {
    require_once __DIR__ . '/../../models/Berkas.php';
    $berkas_model = new Berkas();
    $berkas = $berkas_model->getBySiswaId($siswa['id_siswa'] ?? $siswa['id'] ?? 0);
} catch (Exception $e) {
    $berkas = [];
}
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="bi bi-person-circle text-primary"></i>
                        Detail Siswa
                    </h1>
                    <p class="text-muted mb-0">Informasi lengkap siswa</p>
                </div>
                <div>
                    <a href="/siswa-app/public/siswa" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i>
                        Kembali
                    </a>
                    <a href="/siswa-app/public/siswa/edit/<?= $siswa['id_siswa'] ?? $siswa['id'] ?>" class="btn btn-warning">
                        <i class="bi bi-pencil"></i>
                        Edit
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Student Information -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-info-circle"></i>
                        Informasi Siswa
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td width="150"><strong>NIS:</strong></td>
                                    <td><?= htmlspecialchars($siswa['nis'] ?? 'N/A') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>NISN:</strong></td>
                                    <td><?= htmlspecialchars($siswa['nisn'] ?? 'N/A') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Nama Lengkap:</strong></td>
                                    <td><?= htmlspecialchars($siswa['nama_lengkap'] ?? $siswa['nama'] ?? 'N/A') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Jenis Kelamin:</strong></td>
                                    <td>
                                        <?php
                                        $gender = $siswa['jenis_kelamin'] ?? 'L';
                                        $genderText = $gender === 'L' ? 'Laki-laki' : 'Perempuan';
                                        $genderIcon = $gender === 'L' ? 'bi-gender-male text-primary' : 'bi-gender-female text-danger';
                                        ?>
                                        <i class="bi <?= $genderIcon ?>"></i>
                                        <?= $genderText ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Tempat Lahir:</strong></td>
                                    <td><?= htmlspecialchars($siswa['tempat_lahir'] ?? 'N/A') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Tanggal Lahir:</strong></td>
                                    <td>
                                        <?php
                                        if (!empty($siswa['tanggal_lahir'])) {
                                            echo date('d F Y', strtotime($siswa['tanggal_lahir']));
                                        } else {
                                            echo 'N/A';
                                        }
                                        ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td width="150"><strong>Kelas:</strong></td>
                                    <td>
                                        <?php if (!empty($siswa['nama_kelas'])): ?>
                                            <span class="badge bg-info fs-6">
                                                <?= htmlspecialchars($siswa['nama_kelas']) ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Belum Ada Kelas</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Tahun Masuk:</strong></td>
                                    <td><?= htmlspecialchars($siswa['tahun_masuk'] ?? 'N/A') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <?php
                                        $status = $siswa['status_siswa'] ?? 'aktif';
                                        $statusClass = [
                                            'aktif' => 'bg-success',
                                            'lulus' => 'bg-primary',
                                            'mutasi' => 'bg-warning',
                                            'dropout' => 'bg-danger'
                                        ];
                                        ?>
                                        <span class="badge <?= $statusClass[$status] ?? 'bg-secondary' ?> fs-6">
                                            <?= ucfirst($status) ?>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td><?= htmlspecialchars($siswa['email'] ?? 'N/A') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>No. Telepon:</strong></td>
                                    <td><?= htmlspecialchars($siswa['no_telepon'] ?? 'N/A') ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Dibuat:</strong></td>
                                    <td>
                                        <?php
                                        if (!empty($siswa['created_at'])) {
                                            echo date('d F Y H:i', strtotime($siswa['created_at']));
                                        } else {
                                            echo 'N/A';
                                        }
                                        ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <?php if (!empty($siswa['alamat'])): ?>
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6><strong>Alamat:</strong></h6>
                            <p class="text-muted"><?= nl2br(htmlspecialchars($siswa['alamat'])) ?></p>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Parent Information & Files -->
        <div class="col-lg-4">
            <!-- Parent Information -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-people"></i>
                        Data Orang Tua
                    </h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless table-sm">
                        <tr>
                            <td><strong>Nama Ayah:</strong></td>
                            <td><?= htmlspecialchars($siswa['nama_ayah'] ?? 'N/A') ?></td>
                        </tr>
                        <tr>
                            <td><strong>Pekerjaan Ayah:</strong></td>
                            <td><?= htmlspecialchars($siswa['pekerjaan_ayah'] ?? 'N/A') ?></td>
                        </tr>
                        <tr>
                            <td><strong>Nama Ibu:</strong></td>
                            <td><?= htmlspecialchars($siswa['nama_ibu'] ?? 'N/A') ?></td>
                        </tr>
                        <tr>
                            <td><strong>Pekerjaan Ibu:</strong></td>
                            <td><?= htmlspecialchars($siswa['pekerjaan_ibu'] ?? 'N/A') ?></td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Upload Files -->
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-files"></i>
                        Berkas Siswa
                    </h6>
                    <div class="btn-group">
                        <a href="/siswa-app/public/upload/berkas/<?= $siswa['id_siswa'] ?? $siswa['id'] ?>" class="btn btn-primary btn-sm">
                            <i class="bi bi-upload"></i>
                            Upload Berkas
                        </a>
                        <a href="/siswa-app/public/berkas/index/<?= $siswa['id_siswa'] ?? $siswa['id'] ?>" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-eye"></i>
                            Lihat Semua
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (!empty($berkas)): ?>
                        <?php
                        // Group berkas by category
                        $berkasModel = new Berkas();
                        $fileCategories = $berkasModel->getFileCategories();
                        $allowedTypes = $berkasModel->getAllowedTypes();

                        // Group files by category
                        $groupedBerkas = [];
                        foreach ($berkas as $b) {
                            $category = null;
                            foreach ($fileCategories as $catName => $types) {
                                if (array_key_exists($b['jenis_berkas'], $types)) {
                                    $category = $catName;
                                    break;
                                }
                            }
                            if (!$category) $category = 'Lainnya';
                            $groupedBerkas[$category][] = $b;
                        }
                        ?>

                        <?php foreach ($groupedBerkas as $categoryName => $categoryFiles): ?>
                            <div class="mb-4">
                                <h6 class="text-muted mb-3">
                                    <i class="bi bi-folder"></i>
                                    <?= htmlspecialchars($categoryName) ?>
                                    <span class="badge bg-secondary ms-2"><?= count($categoryFiles) ?></span>
                                </h6>

                                <div class="row">
                                    <?php foreach ($categoryFiles as $b): ?>
                                        <div class="col-md-6 mb-3">
                                            <div class="card border-0 bg-light h-100">
                                                <div class="card-body p-3">
                                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                                        <div class="flex-grow-1">
                                                            <h6 class="card-title mb-1" style="font-size: 14px;">
                                                                <?= htmlspecialchars($b['nama_berkas'] ?? 'File') ?>
                                                            </h6>
                                                            <p class="card-text text-muted small mb-1">
                                                                <?= $allowedTypes[$b['jenis_berkas']] ?? ucfirst(str_replace('_', ' ', $b['jenis_berkas'])) ?>
                                                            </p>
                                                            <small class="text-muted">
                                                                <?= number_format(($b['ukuran_file'] ?? 0) / 1024, 1) ?> KB
                                                                • <?= date('d/m/Y', strtotime($b['created_at'] ?? 'now')) ?>
                                                            </small>
                                                        </div>
                                                        <div class="dropdown">
                                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                                <i class="bi bi-three-dots"></i>
                                                            </button>
                                                            <ul class="dropdown-menu">
                                                                <li>
                                                                    <a class="dropdown-item" href="/siswa-app/public/upload/download/<?= $b['id'] ?>">
                                                                        <i class="bi bi-download"></i> Download
                                                                    </a>
                                                                </li>
                                                                <li>
                                                                    <a class="dropdown-item text-danger" href="/siswa-app/public/upload/delete/<?= $b['id'] ?>"
                                                                       onclick="return confirm('Yakin ingin menghapus file ini?')">
                                                                        <i class="bi bi-trash"></i> Hapus
                                                                    </a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>

                                                    <?php if (!empty($b['keterangan'])): ?>
                                                        <small class="text-muted">
                                                            <i class="bi bi-chat-text"></i>
                                                            <?= htmlspecialchars($b['keterangan']) ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>

                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="bi bi-file-earmark" style="font-size: 3rem; color: #dee2e6;"></i>
                            <h6 class="text-muted mt-3 mb-2">Belum ada berkas</h6>
                            <p class="text-muted small mb-3">Upload berkas pertama untuk siswa ini</p>
                            <a href="/siswa-app/public/upload/berkas/<?= $siswa['id_siswa'] ?? $siswa['id'] ?>" class="btn btn-primary">
                                <i class="bi bi-upload"></i>
                                Upload Berkas Sekarang
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Catatan Siswa Section -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-journal-text"></i>
                            Catatan Siswa
                        </h6>
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addCatatanModal">
                                <i class="bi bi-plus-circle"></i>
                                Tambah Catatan
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Tabs for different categories -->
                    <ul class="nav nav-tabs" id="catatanTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="pamong-tab" data-bs-toggle="tab" data-bs-target="#pamong" type="button" role="tab">
                                <i class="bi bi-person-badge"></i>
                                Catatan Pamong
                                <?php if (!empty($catatan_grouped['pamong'])): ?>
                                    <span class="badge bg-info ms-1"><?= count($catatan_grouped['pamong']) ?></span>
                                <?php endif; ?>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="wali-tab" data-bs-toggle="tab" data-bs-target="#wali" type="button" role="tab">
                                <i class="bi bi-mortarboard"></i>
                                Catatan Wali Kelas
                                <?php if (!empty($catatan_grouped['wali_kelas'])): ?>
                                    <span class="badge bg-success ms-1"><?= count($catatan_grouped['wali_kelas']) ?></span>
                                <?php endif; ?>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="bk-tab" data-bs-toggle="tab" data-bs-target="#bk" type="button" role="tab">
                                <i class="bi bi-heart"></i>
                                Catatan BK
                                <?php if (!empty($catatan_grouped['bk'])): ?>
                                    <span class="badge bg-warning ms-1"><?= count($catatan_grouped['bk']) ?></span>
                                <?php endif; ?>
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content mt-3" id="catatanTabContent">
                        <!-- Pamong Tab -->
                        <div class="tab-pane fade show active" id="pamong" role="tabpanel">
                            <?php if (!empty($catatan_grouped['pamong'])): ?>
                                <?php foreach ($catatan_grouped['pamong'] as $catatan): ?>
                                    <?php include __DIR__ . '/components/catatan_card.php'; ?>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="bi bi-journal" style="font-size: 3rem; color: #dee2e6;"></i>
                                    <h6 class="text-muted mt-3">Belum ada catatan pamong</h6>
                                    <p class="text-muted small">Catatan pamong akan muncul di sini</p>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Wali Kelas Tab -->
                        <div class="tab-pane fade" id="wali" role="tabpanel">
                            <?php if (!empty($catatan_grouped['wali_kelas'])): ?>
                                <?php foreach ($catatan_grouped['wali_kelas'] as $catatan): ?>
                                    <?php include __DIR__ . '/components/catatan_card.php'; ?>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="bi bi-mortarboard" style="font-size: 3rem; color: #dee2e6;"></i>
                                    <h6 class="text-muted mt-3">Belum ada catatan wali kelas</h6>
                                    <p class="text-muted small">Catatan wali kelas akan muncul di sini</p>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- BK Tab -->
                        <div class="tab-pane fade" id="bk" role="tabpanel">
                            <?php if (!empty($catatan_grouped['bk'])): ?>
                                <?php foreach ($catatan_grouped['bk'] as $catatan): ?>
                                    <?php include __DIR__ . '/components/catatan_card.php'; ?>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="bi bi-heart" style="font-size: 3rem; color: #dee2e6;"></i>
                                    <h6 class="text-muted mt-3">Belum ada catatan BK</h6>
                                    <p class="text-muted small">Catatan bimbingan konseling akan muncul di sini</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Add Catatan -->
<div class="modal fade" id="addCatatanModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-plus-circle"></i>
                    Tambah Catatan Siswa
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="/siswa-app/public/catatan/create">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                    <input type="hidden" name="siswa_id" value="<?= $siswa['id_siswa'] ?? $siswa['id'] ?>">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="jenis_catatan" class="form-label">Jenis Catatan <span class="text-danger">*</span></label>
                                <select class="form-select" id="jenis_catatan" name="jenis_catatan" required>
                                    <option value="">Pilih Jenis Catatan</option>
                                    <?php if (isset($catatan_categories)): ?>
                                        <optgroup label="Pamong">
                                            <?php foreach ($catatan_categories['pamong'] as $cat): ?>
                                                <option value="<?= $cat['kode_kategori'] ?>"><?= $cat['nama_kategori'] ?></option>
                                            <?php endforeach; ?>
                                        </optgroup>
                                        <optgroup label="Wali Kelas">
                                            <?php foreach ($catatan_categories['wali_kelas'] as $cat): ?>
                                                <option value="<?= $cat['kode_kategori'] ?>"><?= $cat['nama_kategori'] ?></option>
                                            <?php endforeach; ?>
                                        </optgroup>
                                        <optgroup label="BK">
                                            <?php foreach ($catatan_categories['bk'] as $cat): ?>
                                                <option value="<?= $cat['kode_kategori'] ?>"><?= $cat['nama_kategori'] ?></option>
                                            <?php endforeach; ?>
                                        </optgroup>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tanggal_catatan" class="form-label">Tanggal <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="tanggal_catatan" name="tanggal_catatan"
                                       value="<?= date('Y-m-d') ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tingkat_prioritas" class="form-label">Prioritas</label>
                                <select class="form-select" id="tingkat_prioritas" name="tingkat_prioritas">
                                    <option value="rendah">Rendah</option>
                                    <option value="sedang" selected>Sedang</option>
                                    <option value="tinggi">Tinggi</option>
                                    <option value="urgent">Urgent</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status_catatan" class="form-label">Status</label>
                                <select class="form-select" id="status_catatan" name="status_catatan">
                                    <option value="draft">Draft</option>
                                    <option value="aktif" selected>Aktif</option>
                                    <option value="selesai">Selesai</option>
                                    <option value="ditunda">Ditunda</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="judul_catatan" class="form-label">Judul Catatan <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="judul_catatan" name="judul_catatan" required>
                    </div>

                    <div class="mb-3">
                        <label for="isi_catatan" class="form-label">Isi Catatan <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="isi_catatan" name="isi_catatan" rows="5" required></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="tindak_lanjut" class="form-label">Tindak Lanjut</label>
                        <textarea class="form-control" id="tindak_lanjut" name="tindak_lanjut" rows="3"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="tanggal_tindak_lanjut" class="form-label">Tanggal Tindak Lanjut</label>
                        <input type="date" class="form-control" id="tanggal_tindak_lanjut" name="tanggal_tindak_lanjut">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i>
                        Simpan Catatan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>