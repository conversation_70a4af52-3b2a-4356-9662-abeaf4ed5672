<?php
// Create dummy data for testing berkas and catatan features
require_once 'app/models/Database.php';

try {
    $db = new Database();
    echo "<h2>Creating Dummy Data for Testing</h2>";
    
    // 1. Create berkas table if not exists
    echo "<p>1. Creating berkas table...</p>";
    $db->query("
        CREATE TABLE IF NOT EXISTS berkas (
            id INT PRIMARY KEY AUTO_INCREMENT,
            siswa_id INT NOT NULL,
            jenis_berkas VARCHAR(100) NOT NULL,
            nama_berkas VARCHAR(255) NOT NULL,
            nama_file_asli VARCHAR(255) NOT NULL,
            nama_file_sistem VARCHAR(255) NOT NULL,
            ukuran_file INT NOT NULL DEFAULT 0,
            mime_type VARCHAR(100) NOT NULL DEFAULT 'application/octet-stream',
            file_path VARCHAR(500) NOT NULL,
            file_hash VARCHAR(64) NOT NULL DEFAULT '',
            keterangan TEXT,
            uploaded_by INT DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "<p style='color: green;'>✅ Berkas table created/verified</p>";
    
    // 2. Create catatan_siswa table if not exists
    echo "<p>2. Creating catatan_siswa table...</p>";
    $db->query("
        CREATE TABLE IF NOT EXISTS catatan_siswa (
            id INT PRIMARY KEY AUTO_INCREMENT,
            siswa_id INT NOT NULL,
            jenis_catatan VARCHAR(50) NOT NULL,
            judul_catatan VARCHAR(255) NOT NULL,
            isi_catatan TEXT NOT NULL,
            tanggal_catatan DATE NOT NULL,
            tingkat_prioritas ENUM('rendah', 'sedang', 'tinggi', 'urgent') DEFAULT 'sedang',
            status_catatan ENUM('draft', 'aktif', 'selesai', 'ditunda') DEFAULT 'aktif',
            tindak_lanjut TEXT,
            tanggal_tindak_lanjut DATE,
            created_by INT NOT NULL DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_by INT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "<p style='color: green;'>✅ Catatan_siswa table created/verified</p>";
    
    // 3. Insert dummy berkas data
    echo "<p>3. Inserting dummy berkas data...</p>";
    $berkasData = [
        [15, 'Akta Kelahiran', 'Akta Kelahiran Ahmad Fauzi', 'akta_ahmad_fauzi.pdf', 'akta_15_001.pdf', 'uploads/berkas/', 'Akta kelahiran asli'],
        [15, 'Kartu Keluarga', 'Kartu Keluarga Ahmad Fauzi', 'kk_ahmad_fauzi.pdf', 'kk_15_001.pdf', 'uploads/berkas/', 'Kartu keluarga terbaru'],
        [15, 'Ijazah', 'Ijazah SMP Ahmad Fauzi', 'ijazah_smp_ahmad.pdf', 'ijazah_15_001.pdf', 'uploads/berkas/', 'Ijazah SMP tahun 2023'],
        [15, 'Rapor', 'Rapor Semester 1', 'rapor_sem1_ahmad.pdf', 'rapor_15_001.pdf', 'uploads/berkas/', 'Rapor semester 1 kelas X'],
        [15, 'Foto', 'Foto 3x4 Ahmad Fauzi', 'foto_ahmad_3x4.jpg', 'foto_15_001.jpg', 'uploads/berkas/', 'Foto formal 3x4']
    ];
    
    foreach ($berkasData as $berkas) {
        $db->query("
            INSERT IGNORE INTO berkas (siswa_id, jenis_berkas, nama_berkas, nama_file_asli, nama_file_sistem, file_path, keterangan, uploaded_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, 1)
        ", $berkas);
    }
    echo "<p style='color: green;'>✅ Dummy berkas data inserted</p>";
    
    // 4. Insert dummy catatan data
    echo "<p>4. Inserting dummy catatan data...</p>";
    $catatanData = [
        [15, 'wali_kelas', 'Prestasi Akademik', 'Ahmad menunjukkan prestasi yang baik dalam mata pelajaran Matematika dengan nilai rata-rata 85.', '2024-01-15', 'sedang', 'aktif', 'Pertahankan prestasi dan tingkatkan di mata pelajaran lain'],
        [15, 'pamong', 'Kedisiplinan', 'Ahmad selalu hadir tepat waktu dan mengikuti kegiatan sekolah dengan baik.', '2024-01-20', 'rendah', 'aktif', 'Terus pertahankan kedisiplinan'],
        [15, 'bk', 'Konseling Karir', 'Diskusi mengenai minat dan bakat Ahmad untuk melanjutkan ke perguruan tinggi.', '2024-02-01', 'tinggi', 'aktif', 'Lakukan tes minat bakat dan konseling lanjutan'],
        [15, 'akademik', 'Tugas Proyek', 'Ahmad berhasil menyelesaikan proyek akhir semester dengan baik.', '2024-02-10', 'sedang', 'selesai', null],
        [15, 'prestasi', 'Juara Olimpiade', 'Ahmad meraih juara 2 dalam Olimpiade Matematika tingkat kabupaten.', '2024-02-15', 'tinggi', 'aktif', 'Persiapkan untuk olimpiade tingkat provinsi'],
        [15, 'pelanggaran', 'Terlambat', 'Ahmad terlambat masuk kelas sebanyak 2 kali dalam seminggu.', '2024-02-20', 'sedang', 'aktif', 'Beri peringatan dan pantau kedisiplinan']
    ];
    
    foreach ($catatanData as $catatan) {
        $db->query("
            INSERT IGNORE INTO catatan_siswa (siswa_id, jenis_catatan, judul_catatan, isi_catatan, tanggal_catatan, tingkat_prioritas, status_catatan, tindak_lanjut, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)
        ", $catatan);
    }
    echo "<p style='color: green;'>✅ Dummy catatan data inserted</p>";
    
    // 5. Verify data
    echo "<p>5. Verifying inserted data...</p>";
    $berkasCount = $db->fetch("SELECT COUNT(*) as count FROM berkas WHERE siswa_id = 15");
    $catatanCount = $db->fetch("SELECT COUNT(*) as count FROM catatan_siswa WHERE siswa_id = 15");
    
    echo "<p style='color: blue;'>📊 Berkas count for siswa ID 15: " . $berkasCount['count'] . "</p>";
    echo "<p style='color: blue;'>📊 Catatan count for siswa ID 15: " . $catatanCount['count'] . "</p>";
    
    echo "<h3 style='color: green;'>✅ All dummy data created successfully!</h3>";
    echo "<p><a href='/siswa-app/public/siswa/detail/15' target='_blank'>🔗 Test the detail page now</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
