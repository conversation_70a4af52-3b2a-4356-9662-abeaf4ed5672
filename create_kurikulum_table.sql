-- Create Kurikulum Table
CREATE TABLE IF NOT EXISTS kurikulum (
    id_kurikulum INT AUTO_INCREMENT PRIMARY KEY,
    nama_kurikulum VARCHAR(100) NOT NULL,
    kode_kurikulum VARCHAR(20) NOT NULL UNIQUE,
    deskripsi TEXT,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default kurikulum data
INSERT INTO kurikulum (nama_kurikulum, kode_kurikulum, deskripsi, is_active) VALUES
('Kurikulum Merdeka', 'MERDEKA', 'Kurikulum yang memberikan kebebasan belajar kepada siswa untuk mengeksplorasi minat dan bakat mereka', 1),
('Kurikulum Seminari', 'SEMINARI', 'Kurikulum khusus untuk pendidikan seminari dengan fokus pada pembentukan karakter dan spiritualitas', 1);

-- Add kurikulum_id column to kelas table
ALTER TABLE kelas ADD COLUMN kurikulum_id INT DEFAULT NULL AFTER tingkat;

-- Add foreign key constraint
ALTER TABLE kelas ADD CONSTRAINT fk_kelas_kurikulum 
    FOREIGN KEY (kurikulum_id) REFERENCES kurikulum(id_kurikulum) 
    ON DELETE SET NULL ON UPDATE CASCADE;

-- Create index for better performance
CREATE INDEX idx_kelas_kurikulum ON kelas(kurikulum_id);
CREATE INDEX idx_kurikulum_active ON kurikulum(is_active);

-- Migrate existing jurusan data to kurikulum
-- Map existing jurusan to Kurikulum Merdeka as default
UPDATE kelas SET kurikulum_id = 1 WHERE kurikulum_id IS NULL;

-- Show current structure
DESCRIBE kelas;
DESCRIBE kurikulum;
