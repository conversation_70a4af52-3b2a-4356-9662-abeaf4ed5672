<?php
/**
 * Debug Kelas Access Control
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'app/helpers/Security.php';
require_once 'app/helpers/SimpleSessionManager.php';
require_once 'app/models/Kelas.php';

$sessionManager = new SimpleSessionManager();

// Check authentication
if (!Security::isAuthenticated()) {
    echo "<p style='color: red;'>❌ Not authenticated. <a href='simple_login.php'>Please login first</a></p>";
    exit;
}

$currentUser = $sessionManager->getCurrentUser();
$kelasModel = new Kelas();

?>
<!DOCTYPE html>
<html>
<head>
    <title>Debug Kelas Access Control</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>🔍 Debug Kelas Access Control</h1>
    
    <div class="debug-section">
        <h2>👤 Current User Info</h2>
        <table>
            <tr><th>Property</th><th>Value</th></tr>
            <tr><td>User ID</td><td><?= htmlspecialchars($currentUser['id'] ?? 'N/A') ?></td></tr>
            <tr><td>Username</td><td><?= htmlspecialchars($currentUser['username'] ?? 'N/A') ?></td></tr>
            <tr><td>Role</td><td><?= htmlspecialchars($currentUser['role'] ?? 'N/A') ?></td></tr>
            <tr><td>Nama Lengkap</td><td><?= htmlspecialchars($currentUser['nama_lengkap'] ?? 'N/A') ?></td></tr>
        </table>
    </div>

    <div class="debug-section">
        <h2>🔒 Session Data</h2>
        <table>
            <tr><th>Session Key</th><th>Value</th></tr>
            <tr><td>$_SESSION['user_role']</td><td><?= htmlspecialchars($_SESSION['user_role'] ?? 'NOT SET') ?></td></tr>
            <tr><td>$_SESSION['role']</td><td><?= htmlspecialchars($_SESSION['role'] ?? 'NOT SET') ?></td></tr>
            <tr><td>$_SESSION['username']</td><td><?= htmlspecialchars($_SESSION['username'] ?? 'NOT SET') ?></td></tr>
            <tr><td>$_SESSION['user_id']</td><td><?= htmlspecialchars($_SESSION['user_id'] ?? 'NOT SET') ?></td></tr>
        </table>
    </div>

    <div class="debug-section">
        <h2>📊 All Kelas (Admin View)</h2>
        <?php
        $allKelas = $kelasModel->getAll();
        if (empty($allKelas)) {
            echo "<p class='error'>❌ No kelas found in database</p>";
        } else {
            echo "<table>";
            echo "<tr><th>ID</th><th>Nama Kelas</th><th>Tingkat</th><th>Jurusan</th><th>Wali Kelas</th><th>Status</th></tr>";
            foreach ($allKelas as $k) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($k['id_kelas'] ?? '') . "</td>";
                echo "<td>" . htmlspecialchars($k['nama_kelas'] ?? '') . "</td>";
                echo "<td>" . htmlspecialchars($k['tingkat'] ?? '') . "</td>";
                echo "<td>" . htmlspecialchars($k['jurusan'] ?? '-') . "</td>";
                echo "<td>" . htmlspecialchars($k['wali_kelas'] ?? '-') . "</td>";
                echo "<td>" . htmlspecialchars($k['is_active'] ? 'Active' : 'Inactive') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        ?>
    </div>

    <div class="debug-section">
        <h2>🎯 Role-based Kelas Access Test</h2>
        <?php
        $userRole = $_SESSION['user_role'] ?? $_SESSION['role'] ?? '';
        $userName = $_SESSION['username'] ?? '';
        
        echo "<p><strong>Testing with:</strong></p>";
        echo "<ul>";
        echo "<li>Role: " . htmlspecialchars($userRole) . "</li>";
        echo "<li>Username: " . htmlspecialchars($userName) . "</li>";
        echo "</ul>";

        // Test role-based access
        switch($userRole) {
            case 'admin':
                echo "<p class='success'>✅ Admin - Should see all kelas</p>";
                $filteredKelas = $kelasModel->getAll();
                break;
                
            case 'pamong':
                echo "<p class='info'>👨‍🏫 Pamong - Testing level-based access</p>";
                $pamongLevel = '';
                if (strpos($userName, 'MP') !== false) {
                    $pamongLevel = 'KPP';
                    echo "<p>MP detected - should see KPP classes</p>";
                } elseif (strpos($userName, 'MT') !== false) {
                    $pamongLevel = 'X';
                    echo "<p>MT detected - should see X classes</p>";
                } elseif (strpos($userName, 'MM') !== false) {
                    $pamongLevel = 'XI';
                    echo "<p>MM detected - should see XI classes</p>";
                } elseif (strpos($userName, 'MU') !== false) {
                    $pamongLevel = 'XII,KPA';
                    echo "<p>MU detected - should see XII and KPA classes</p>";
                } else {
                    echo "<p class='error'>❌ No pamong level detected in username</p>";
                }
                
                if ($pamongLevel) {
                    $filteredKelas = $kelasModel->getByTingkat($pamongLevel);
                } else {
                    $filteredKelas = [];
                }
                break;
                
            case 'wali_kelas':
                echo "<p class='info'>👩‍🏫 Wali Kelas - Should see own classes only</p>";
                $filteredKelas = $kelasModel->getByWaliKelas($userName);
                break;
                
            case 'staff':
                echo "<p class='info'>👤 Staff - Should see all kelas (read-only)</p>";
                $filteredKelas = $kelasModel->getAll();
                break;
                
            default:
                echo "<p class='error'>❌ Unknown role: " . htmlspecialchars($userRole) . "</p>";
                $filteredKelas = [];
        }

        echo "<h3>Filtered Kelas Result:</h3>";
        if (empty($filteredKelas)) {
            echo "<p class='error'>❌ No kelas accessible for this role</p>";
        } else {
            echo "<table>";
            echo "<tr><th>ID</th><th>Nama Kelas</th><th>Tingkat</th><th>Jurusan</th><th>Wali Kelas</th></tr>";
            foreach ($filteredKelas as $k) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($k['id_kelas'] ?? '') . "</td>";
                echo "<td>" . htmlspecialchars($k['nama_kelas'] ?? '') . "</td>";
                echo "<td>" . htmlspecialchars($k['tingkat'] ?? '') . "</td>";
                echo "<td>" . htmlspecialchars($k['jurusan'] ?? '-') . "</td>";
                echo "<td>" . htmlspecialchars($k['wali_kelas'] ?? '-') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        ?>
    </div>

    <div class="debug-section">
        <h2>🔧 Actions</h2>
        <p>
            <a href="public/kelas" style="background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;">
                🏫 Go to Kelas Management
            </a>
            <a href="simple_login.php?logout=1" style="background: #dc3545; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-left: 10px;">
                🚪 Logout
            </a>
        </p>
    </div>

</body>
</html>
