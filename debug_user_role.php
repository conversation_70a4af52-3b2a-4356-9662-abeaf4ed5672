<?php
/**
 * Debug User Role and Permissions
 */

require_once 'app/helpers/Security.php';
require_once 'app/helpers/SimpleSessionManager.php';

$sessionManager = new SimpleSessionManager();

?>
<!DOCTYPE html>
<html>
<head>
    <title>Debug User Role</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .btn { padding: 8px 16px; margin: 5px; text-decoration: none; border-radius: 4px; display: inline-block; }
        .btn-primary { background: #007bff; color: white; }
        .btn-danger { background: #dc3545; color: white; }
    </style>
</head>
<body>
    <h1>🔍 Debug User Role & Permissions</h1>
    
    <div class="debug-section">
        <h2>🔐 Authentication Status</h2>
        <?php
        $isAuth = Security::isAuthenticated();
        echo "<p><strong>Is Authenticated:</strong> " . ($isAuth ? '<span class="success">✅ YES</span>' : '<span class="error">❌ NO</span>') . "</p>";
        
        if (!$isAuth) {
            echo "<p class='error'>❌ User is not authenticated. <a href='simple_login.php'>Please login first</a></p>";
        }
        ?>
    </div>

    <div class="debug-section">
        <h2>📋 Session Data</h2>
        <table>
            <tr><th>Session Key</th><th>Value</th></tr>
            <?php
            foreach ($_SESSION as $key => $value) {
                $displayValue = is_array($value) ? json_encode($value) : htmlspecialchars($value);
                echo "<tr><td><strong>$key</strong></td><td>$displayValue</td></tr>";
            }
            ?>
        </table>
    </div>

    <div class="debug-section">
        <h2>👤 Current User Info</h2>
        <?php
        if ($isAuth) {
            $currentUser = $sessionManager->getCurrentUser();
            echo "<table>";
            echo "<tr><th>Property</th><th>Value</th></tr>";
            foreach ($currentUser as $key => $value) {
                $displayValue = is_array($value) ? json_encode($value) : htmlspecialchars($value);
                echo "<tr><td><strong>$key</strong></td><td>$displayValue</td></tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='error'>❌ No user data available</p>";
        }
        ?>
    </div>

    <div class="debug-section">
        <h2>🔧 Permission Tests</h2>
        <?php
        if ($isAuth) {
            $currentUser = $sessionManager->getCurrentUser();
            $userRole = $currentUser['role'] ?? '';
            
            // Test canManageKelas function
            function canManageKelas($role) {
                return $role === 'admin';
            }
            
            $canManage = canManageKelas($userRole);
            
            echo "<table>";
            echo "<tr><th>Test</th><th>Result</th></tr>";
            echo "<tr><td>User Role</td><td><strong>" . htmlspecialchars($userRole) . "</strong></td></tr>";
            echo "<tr><td>Can Manage Kelas</td><td>" . ($canManage ? '<span class="success">✅ YES</span>' : '<span class="error">❌ NO</span>') . "</td></tr>";
            echo "<tr><td>Can Delete Kelas</td><td>" . ($canManage ? '<span class="success">✅ YES</span>' : '<span class="error">❌ NO</span>') . "</td></tr>";
            echo "<tr><td>Should See Delete Button</td><td>" . ($canManage ? '<span class="success">✅ YES</span>' : '<span class="error">❌ NO</span>') . "</td></tr>";
            echo "</table>";
            
            if (!$canManage) {
                echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
                echo "<strong>⚠️ Permission Issue:</strong> Current user role '$userRole' does not have permission to delete kelas. Only 'admin' role can delete kelas.";
                echo "</div>";
            }
        } else {
            echo "<p class='error'>❌ Cannot test permissions - user not authenticated</p>";
        }
        ?>
    </div>

    <div class="debug-section">
        <h2>🎯 Role Mapping</h2>
        <table>
            <tr><th>Role</th><th>Can Manage Kelas</th><th>Can Delete</th><th>Description</th></tr>
            <tr>
                <td><strong>admin</strong></td>
                <td><span class="success">✅ YES</span></td>
                <td><span class="success">✅ YES</span></td>
                <td>Full access to all kelas management</td>
            </tr>
            <tr>
                <td><strong>pamong</strong></td>
                <td><span class="error">❌ NO</span></td>
                <td><span class="error">❌ NO</span></td>
                <td>Read-only access to assigned tingkat</td>
            </tr>
            <tr>
                <td><strong>wali_kelas</strong></td>
                <td><span class="error">❌ NO</span></td>
                <td><span class="error">❌ NO</span></td>
                <td>Read-only access to own kelas</td>
            </tr>
            <tr>
                <td><strong>staff</strong></td>
                <td><span class="error">❌ NO</span></td>
                <td><span class="error">❌ NO</span></td>
                <td>Read-only access to all kelas</td>
            </tr>
        </table>
    </div>

    <div class="debug-section">
        <h2>🧪 Test Actions</h2>
        <?php if ($isAuth): ?>
            <p>
                <a href="public/kelas" class="btn btn-primary">🏫 Go to Kelas Management</a>
                <?php if (isset($currentUser) && $currentUser['role'] === 'admin'): ?>
                    <a href="public/kelas/create" class="btn btn-primary">➕ Add New Kelas</a>
                <?php endif; ?>
                <a href="simple_login.php?logout=1" class="btn btn-danger">🚪 Logout</a>
            </p>
        <?php else: ?>
            <p>
                <a href="simple_login.php" class="btn btn-primary">🔑 Login</a>
            </p>
        <?php endif; ?>
    </div>

    <div class="debug-section">
        <h2>💡 Troubleshooting Tips</h2>
        <h3>If delete button is not showing:</h3>
        <ol>
            <li><strong>Check Role:</strong> Make sure you're logged in as 'admin'</li>
            <li><strong>Check Session:</strong> Verify $_SESSION['role'] or $_SESSION['user_role'] is 'admin'</li>
            <li><strong>Check Controller:</strong> Verify $can_manage variable is passed to view</li>
            <li><strong>Check View:</strong> Verify condition <?php echo htmlspecialchars('<?php if (isset($can_manage) && $can_manage): ?>'); ?> is working</li>
            <li><strong>Check JavaScript:</strong> Verify confirmDelete() function is defined</li>
        </ol>
        
        <h3>If delete button shows but doesn't work:</h3>
        <ol>
            <li><strong>Check JavaScript Console:</strong> Look for JavaScript errors</li>
            <li><strong>Check CSRF Token:</strong> Verify token is generated and passed correctly</li>
            <li><strong>Check Network:</strong> Verify POST request is sent to correct URL</li>
            <li><strong>Check Controller:</strong> Verify delete method handles POST requests</li>
            <li><strong>Check Database:</strong> Verify deleteKelas() method works</li>
        </ol>
    </div>

    <script>
    // Auto-refresh every 10 seconds to see updated session data
    setTimeout(() => {
        location.reload();
    }, 10000);
    
    // Test JavaScript functions
    console.log('Debug page loaded');
    console.log('Current session data:', <?= json_encode($_SESSION) ?>);
    </script>

</body>
</html>
