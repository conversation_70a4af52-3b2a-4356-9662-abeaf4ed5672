<?php
/**
 * Script untuk memperbaiki data tingkat yang tidak valid
 */

require_once 'app/config/db.php';

try {
    $pdo = getDBConnection();
    
    echo "=== FIX TINGKAT DATA ===\n";
    
    // Check data with invalid tingkat
    $invalidData = $pdo->query("
        SELECT id, nama_kelas, tingkat, jurusan 
        FROM kelas 
        WHERE tingkat NOT IN ('KPP', 'X', 'XI', 'XII', 'KPA')
        ORDER BY id
    ")->fetchAll();
    
    if (empty($invalidData)) {
        echo "✅ No invalid tingkat data found.\n";
    } else {
        echo "Found " . count($invalidData) . " records with invalid tingkat:\n";
        
        foreach ($invalidData as $row) {
            echo "- ID: {$row['id']}, Nama: {$row['nama_kelas']}, Tingkat: '{$row['tingkat']}', <PERSON><PERSON><PERSON>: {$row['jurusan']}\n";
        }
        
        echo "\nFixing invalid data...\n";
        
        // Fix based on class name patterns
        $fixes = 0;
        
        foreach ($invalidData as $row) {
            $newTingkat = null;
            $namaKelas = $row['nama_kelas'];
            
            // Determine tingkat based on class name
            if (strpos($namaKelas, 'KPP') !== false) {
                $newTingkat = 'KPP';
            } elseif (strpos($namaKelas, 'X-') === 0 || strpos($namaKelas, '10') !== false) {
                $newTingkat = 'X';
            } elseif (strpos($namaKelas, 'XI-') === 0 || strpos($namaKelas, '11') !== false) {
                $newTingkat = 'XI';
            } elseif (strpos($namaKelas, 'XII-') === 0 || strpos($namaKelas, '12') !== false) {
                $newTingkat = 'XII';
            } elseif (strpos($namaKelas, 'KPA') !== false) {
                $newTingkat = 'KPA';
            } else {
                // Default to X if can't determine
                $newTingkat = 'X';
            }
            
            // Update the record
            $stmt = $pdo->prepare("UPDATE kelas SET tingkat = ? WHERE id = ?");
            $result = $stmt->execute([$newTingkat, $row['id']]);
            
            if ($result) {
                echo "✓ Updated '{$namaKelas}' from '{$row['tingkat']}' to '$newTingkat'\n";
                $fixes++;
            } else {
                echo "✗ Failed to update '{$namaKelas}'\n";
            }
        }
        
        echo "\nFixed $fixes records.\n";
    }
    
    // Show final statistics
    echo "\n=== FINAL STATISTICS ===\n";
    $stats = $pdo->query("
        SELECT tingkat, COUNT(*) as count 
        FROM kelas 
        WHERE is_active = 1 
        GROUP BY tingkat 
        ORDER BY 
            CASE tingkat 
                WHEN 'KPP' THEN 1
                WHEN 'X' THEN 2
                WHEN 'XI' THEN 3
                WHEN 'XII' THEN 4
                WHEN 'KPA' THEN 5
                ELSE 6
            END
    ")->fetchAll();
    
    $tingkatLabels = [
        'KPP' => 'KPP (Kelas Persiapan Pertama)',
        'X' => 'Kelas X (Kelas 10)',
        'XI' => 'Kelas XI (Kelas 11)',
        'XII' => 'Kelas XII (Kelas 12)',
        'KPA' => 'KPA (Kelas Persiapan Atas)'
    ];
    
    $totalKelas = 0;
    foreach ($stats as $row) {
        $tingkat = $row['tingkat'];
        $label = $tingkatLabels[$tingkat] ?? "Unknown ($tingkat)";
        $count = $row['count'];
        echo "- $label: $count kelas\n";
        $totalKelas += $count;
    }
    
    echo "\nTotal kelas aktif: $totalKelas\n";
    
    // Show sample data for each tingkat
    echo "\n=== SAMPLE DATA BY TINGKAT ===\n";
    foreach (['KPP', 'X', 'XI', 'XII', 'KPA'] as $tingkat) {
        $samples = $pdo->prepare("
            SELECT nama_kelas, jurusan, wali_kelas 
            FROM kelas 
            WHERE tingkat = ? AND is_active = 1 
            ORDER BY nama_kelas 
            LIMIT 3
        ");
        $samples->execute([$tingkat]);
        $sampleData = $samples->fetchAll();
        
        if (!empty($sampleData)) {
            $label = $tingkatLabels[$tingkat];
            echo "\n$label:\n";
            foreach ($sampleData as $sample) {
                echo "  - {$sample['nama_kelas']} ({$sample['jurusan']}) - {$sample['wali_kelas']}\n";
            }
        }
    }
    
    echo "\n✅ Data cleanup completed!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
