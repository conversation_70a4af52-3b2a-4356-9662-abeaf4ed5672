<?php
/**
 * Script untuk menambahkan data sample kelas dengan tingkat baru
 */

require_once 'app/config/db.php';

try {
    $pdo = getDBConnection();
    
    // Sample data kelas dengan tingkat baru
    $kelasData = [
        // KPP - Kelas <PERSON>pan Pertama
        [
            'nama_kelas' => 'KPP-A',
            'tingkat' => 'KPP',
            'jurusan' => 'Umum',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'Dra. Siti Nurhaliza',
            'kapasitas' => 25
        ],
        [
            'nama_kelas' => 'KPP-B',
            'tingkat' => 'KPP',
            'jurusan' => 'Umum',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => '<PERSON>, S.Pd',
            'kapasitas' => 25
        ],
        
        // Kelas X (mengganti format lama)
        [
            'nama_kelas' => 'X-MIPA-1',
            'tingkat' => 'X',
            'jurusan' => 'IPA',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'Dr. <PERSON>, M.Pd',
            'kapasitas' => 32
        ],
        [
            'nama_kelas' => 'X-MIPA-2',
            'tingkat' => 'X',
            'jurusan' => 'IPA',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'Dra. Rina Marlina',
            'kapasitas' => 30
        ],
        [
            'nama_kelas' => 'X-IPS-1',
            'tingkat' => 'X',
            'jurusan' => 'IPS',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'Siti Aminah, S.Pd',
            'kapasitas' => 28
        ],
        [
            'nama_kelas' => 'X-Bahasa',
            'tingkat' => 'X',
            'jurusan' => 'Bahasa',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'Dewi Sartika, S.S',
            'kapasitas' => 24
        ],
        
        // Kelas XI
        [
            'nama_kelas' => 'XI-MIPA-1',
            'tingkat' => 'XI',
            'jurusan' => 'IPA',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'Ahmad Rahman, S.Pd',
            'kapasitas' => 30
        ],
        [
            'nama_kelas' => 'XI-IPS-1',
            'tingkat' => 'XI',
            'jurusan' => 'IPS',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'Rudi Hartono, S.Pd',
            'kapasitas' => 26
        ],
        [
            'nama_kelas' => 'XI-Bahasa',
            'tingkat' => 'XI',
            'jurusan' => 'Bahasa',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'Maya Sari, S.S',
            'kapasitas' => 22
        ],
        
        // Kelas XII
        [
            'nama_kelas' => 'XII-MIPA-1',
            'tingkat' => 'XII',
            'jurusan' => 'IPA',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'Prof. Dr. Indra Wijaya',
            'kapasitas' => 28
        ],
        [
            'nama_kelas' => 'XII-MIPA-2',
            'tingkat' => 'XII',
            'jurusan' => 'IPA',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'Drs. Bambang Sutrisno',
            'kapasitas' => 30
        ],
        [
            'nama_kelas' => 'XII-IPS-1',
            'tingkat' => 'XII',
            'jurusan' => 'IPS',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'Hendra Gunawan, S.Pd',
            'kapasitas' => 25
        ],
        
        // KPA - Kelas Persiapan Atas
        [
            'nama_kelas' => 'KPA-Sains',
            'tingkat' => 'KPA',
            'jurusan' => 'IPA',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'Dr. Lestari Indah, M.Sc',
            'kapasitas' => 20
        ],
        [
            'nama_kelas' => 'KPA-Sosial',
            'tingkat' => 'KPA',
            'jurusan' => 'IPS',
            'tahun_pelajaran' => '2024/2025',
            'wali_kelas' => 'Drs. Agus Salim, M.A',
            'kapasitas' => 18
        ]
    ];
    
    // Check if kelas table exists
    $checkTable = $pdo->query("SHOW TABLES LIKE 'kelas'");
    if ($checkTable->rowCount() == 0) {
        echo "Tabel kelas tidak ditemukan. Pastikan database sudah diimport.\n";
        exit;
    }
    
    $insertedCount = 0;
    $skippedCount = 0;
    
    foreach ($kelasData as $kelas) {
        // Check if class already exists
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM kelas WHERE nama_kelas = ? AND tahun_pelajaran = ?");
        $stmt->execute([$kelas['nama_kelas'], $kelas['tahun_pelajaran']]);
        $exists = $stmt->fetch();
        
        if ($exists['count'] == 0) {
            // Insert new class
            $insertStmt = $pdo->prepare("
                INSERT INTO kelas (nama_kelas, tingkat, jurusan, tahun_pelajaran, wali_kelas, kapasitas, created_by, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, 1, NOW())
            ");
            
            $result = $insertStmt->execute([
                $kelas['nama_kelas'],
                $kelas['tingkat'],
                $kelas['jurusan'],
                $kelas['tahun_pelajaran'],
                $kelas['wali_kelas'],
                $kelas['kapasitas']
            ]);
            
            if ($result) {
                echo "✓ Berhasil menambahkan kelas: " . $kelas['nama_kelas'] . " (Tingkat " . $kelas['tingkat'] . ")\n";
                $insertedCount++;
            } else {
                echo "✗ Gagal menambahkan kelas: " . $kelas['nama_kelas'] . "\n";
            }
        } else {
            echo "- Kelas " . $kelas['nama_kelas'] . " sudah ada, dilewati.\n";
            $skippedCount++;
        }
    }
    
    echo "\n=== SUMMARY ===\n";
    echo "Total kelas baru yang ditambahkan: $insertedCount\n";
    echo "Total kelas yang dilewati: $skippedCount\n";
    
    // Show current data grouped by tingkat
    $allKelas = $pdo->query("
        SELECT nama_kelas, tingkat, jurusan, wali_kelas 
        FROM kelas 
        WHERE is_active = 1 
        ORDER BY 
            CASE tingkat 
                WHEN 'KPP' THEN 1
                WHEN 'X' THEN 2
                WHEN 'XI' THEN 3
                WHEN 'XII' THEN 4
                WHEN 'KPA' THEN 5
                ELSE 6
            END,
            nama_kelas
    ")->fetchAll();
    
    echo "Total kelas aktif saat ini: " . count($allKelas) . "\n\n";
    
    // Group by tingkat
    $kelasByTingkat = [];
    foreach ($allKelas as $k) {
        $tingkat = $k['tingkat'];
        if (!isset($kelasByTingkat[$tingkat])) {
            $kelasByTingkat[$tingkat] = [];
        }
        $kelasByTingkat[$tingkat][] = $k;
    }
    
    // Display grouped data
    $tingkatLabels = [
        'KPP' => 'KPP (Kelas Persiapan Pertama)',
        'X' => 'Kelas X (Kelas 10)',
        'XI' => 'Kelas XI (Kelas 11)',
        'XII' => 'Kelas XII (Kelas 12)',
        'KPA' => 'KPA (Kelas Persiapan Atas)'
    ];
    
    foreach (['KPP', 'X', 'XI', 'XII', 'KPA'] as $tingkat) {
        if (isset($kelasByTingkat[$tingkat])) {
            echo "=== " . $tingkatLabels[$tingkat] . " ===\n";
            foreach ($kelasByTingkat[$tingkat] as $k) {
                echo "- " . $k['nama_kelas'] . " (" . $k['jurusan'] . ") - Wali: " . $k['wali_kelas'] . "\n";
            }
            echo "\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Pastikan database sudah dikonfigurasi dengan benar.\n";
}
?>
