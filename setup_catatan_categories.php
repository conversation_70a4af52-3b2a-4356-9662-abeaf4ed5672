<?php
/**
 * Setup Catatan Categories with Role-based Access
 */

require_once 'app/models/Database.php';

try {
    $db = new Database();
    
    echo "<h2>🔧 Setup Kategori Catatan dengan Role-based Access</h2>";
    
    // Clear existing categories
    $db->execute("DELETE FROM kategori_catatan");
    echo "<p>✅ Cleared existing categories</p>";
    
    // Define categories with role-based access
    $categories = [
        // Pamong Categories
        [
            'kode_kategori' => 'pamong_mp',
            'nama_kategori' => 'Catatan Pamong MP (KPP)',
            'deskripsi' => 'Catatan dari Pamong untuk siswa tingkat KPP',
            'warna_badge' => '#17a2b8',
            'icon_class' => 'bi-person-badge',
            'allowed_roles' => json_encode(['admin', 'pamong'])
        ],
        [
            'kode_kategori' => 'pamong_mt',
            'nama_kategori' => 'Catatan Pamong MT (X)',
            'deskripsi' => 'Catatan dari Pamong untuk siswa tingkat X',
            'warna_badge' => '#28a745',
            'icon_class' => 'bi-person-badge',
            'allowed_roles' => json_encode(['admin', 'pamong'])
        ],
        [
            'kode_kategori' => 'pamong_mm',
            'nama_kategori' => 'Catatan Pamong MM (XI)',
            'deskripsi' => 'Catatan dari Pamong untuk siswa tingkat XI',
            'warna_badge' => '#ffc107',
            'icon_class' => 'bi-person-badge',
            'allowed_roles' => json_encode(['admin', 'pamong'])
        ],
        [
            'kode_kategori' => 'pamong_mu',
            'nama_kategori' => 'Catatan Pamong MU (XII & KPA)',
            'deskripsi' => 'Catatan dari Pamong untuk siswa tingkat XII dan KPA',
            'warna_badge' => '#dc3545',
            'icon_class' => 'bi-person-badge',
            'allowed_roles' => json_encode(['admin', 'pamong'])
        ],
        
        // Wali Kelas Categories
        [
            'kode_kategori' => 'wali_kpp',
            'nama_kategori' => 'Catatan Wali Kelas KPP',
            'deskripsi' => 'Catatan dari Wali Kelas untuk siswa KPP',
            'warna_badge' => '#6f42c1',
            'icon_class' => 'bi-person-check',
            'allowed_roles' => json_encode(['admin', 'wali_kelas'])
        ],
        [
            'kode_kategori' => 'wali_x',
            'nama_kategori' => 'Catatan Wali Kelas X',
            'deskripsi' => 'Catatan dari Wali Kelas untuk siswa kelas X',
            'warna_badge' => '#6f42c1',
            'icon_class' => 'bi-person-check',
            'allowed_roles' => json_encode(['admin', 'wali_kelas'])
        ],
        [
            'kode_kategori' => 'wali_xi',
            'nama_kategori' => 'Catatan Wali Kelas XI',
            'deskripsi' => 'Catatan dari Wali Kelas untuk siswa kelas XI',
            'warna_badge' => '#6f42c1',
            'icon_class' => 'bi-person-check',
            'allowed_roles' => json_encode(['admin', 'wali_kelas'])
        ],
        [
            'kode_kategori' => 'wali_xii',
            'nama_kategori' => 'Catatan Wali Kelas XII',
            'deskripsi' => 'Catatan dari Wali Kelas untuk siswa kelas XII',
            'warna_badge' => '#6f42c1',
            'icon_class' => 'bi-person-check',
            'allowed_roles' => json_encode(['admin', 'wali_kelas'])
        ],
        [
            'kode_kategori' => 'wali_kpa',
            'nama_kategori' => 'Catatan Wali Kelas KPA',
            'deskripsi' => 'Catatan dari Wali Kelas untuk siswa KPA',
            'warna_badge' => '#6f42c1',
            'icon_class' => 'bi-person-check',
            'allowed_roles' => json_encode(['admin', 'wali_kelas'])
        ],
        
        // BK Categories
        [
            'kode_kategori' => 'bk_konseling',
            'nama_kategori' => 'Konseling BK',
            'deskripsi' => 'Catatan konseling dari Bimbingan Konseling',
            'warna_badge' => '#fd7e14',
            'icon_class' => 'bi-chat-heart',
            'allowed_roles' => json_encode(['admin', 'bk'])
        ],
        [
            'kode_kategori' => 'bk_pelanggaran',
            'nama_kategori' => 'Pelanggaran (BK)',
            'deskripsi' => 'Catatan pelanggaran yang ditangani BK',
            'warna_badge' => '#dc3545',
            'icon_class' => 'bi-exclamation-triangle',
            'allowed_roles' => json_encode(['admin', 'bk'])
        ],
        [
            'kode_kategori' => 'bk_prestasi',
            'nama_kategori' => 'Prestasi (BK)',
            'deskripsi' => 'Catatan prestasi yang dicatat BK',
            'warna_badge' => '#28a745',
            'icon_class' => 'bi-trophy',
            'allowed_roles' => json_encode(['admin', 'bk'])
        ],
        [
            'kode_kategori' => 'bk_lainnya',
            'nama_kategori' => 'Catatan BK Lainnya',
            'deskripsi' => 'Catatan lainnya dari Bimbingan Konseling',
            'warna_badge' => '#6c757d',
            'icon_class' => 'bi-journal-text',
            'allowed_roles' => json_encode(['admin', 'bk'])
        ],
        
        // General Categories
        [
            'kode_kategori' => 'akademik',
            'nama_kategori' => 'Akademik',
            'deskripsi' => 'Catatan terkait prestasi dan perkembangan akademik',
            'warna_badge' => '#007bff',
            'icon_class' => 'bi-book',
            'allowed_roles' => json_encode(['admin', 'pamong', 'wali_kelas'])
        ],
        [
            'kode_kategori' => 'prestasi',
            'nama_kategori' => 'Prestasi',
            'deskripsi' => 'Catatan prestasi dan pencapaian siswa',
            'warna_badge' => '#28a745',
            'icon_class' => 'bi-award',
            'allowed_roles' => json_encode(['admin', 'pamong', 'wali_kelas', 'bk'])
        ],
        [
            'kode_kategori' => 'pelanggaran',
            'nama_kategori' => 'Pelanggaran',
            'deskripsi' => 'Catatan pelanggaran dan tindakan disiplin',
            'warna_badge' => '#dc3545',
            'icon_class' => 'bi-shield-exclamation',
            'allowed_roles' => json_encode(['admin', 'pamong', 'wali_kelas', 'bk'])
        ],
        [
            'kode_kategori' => 'kesehatan',
            'nama_kategori' => 'Kesehatan',
            'deskripsi' => 'Catatan terkait kesehatan dan kondisi medis',
            'warna_badge' => '#e83e8c',
            'icon_class' => 'bi-heart-pulse',
            'allowed_roles' => json_encode(['admin', 'pamong', 'wali_kelas'])
        ],
        [
            'kode_kategori' => 'kehadiran',
            'nama_kategori' => 'Kehadiran',
            'deskripsi' => 'Catatan terkait kehadiran dan absensi',
            'warna_badge' => '#20c997',
            'icon_class' => 'bi-calendar-check',
            'allowed_roles' => json_encode(['admin', 'pamong', 'wali_kelas'])
        ],
        [
            'kode_kategori' => 'umum',
            'nama_kategori' => 'Umum',
            'deskripsi' => 'Catatan umum lainnya',
            'warna_badge' => '#6c757d',
            'icon_class' => 'bi-chat-dots',
            'allowed_roles' => json_encode(['admin', 'pamong', 'wali_kelas', 'bk'])
        ]
    ];
    
    // Insert categories
    foreach ($categories as $category) {
        $sql = "INSERT INTO kategori_catatan (kode_kategori, nama_kategori, deskripsi, warna_badge, icon_class, allowed_roles, is_active) 
                VALUES (?, ?, ?, ?, ?, ?, 1)";
        
        $params = [
            $category['kode_kategori'],
            $category['nama_kategori'],
            $category['deskripsi'],
            $category['warna_badge'],
            $category['icon_class'],
            $category['allowed_roles']
        ];
        
        $db->execute($sql, $params);
        echo "<p>✅ Added: {$category['nama_kategori']}</p>";
    }
    
    echo "<h3>🎉 Setup Complete!</h3>";
    echo "<p><strong>Total categories created:</strong> " . count($categories) . "</p>";
    
    // Show summary by role
    echo "<h4>📋 Summary by Role:</h4>";
    echo "<ul>";
    echo "<li><strong>Admin:</strong> Can create all types of catatan</li>";
    echo "<li><strong>Pamong MP:</strong> Can create pamong_mp, wali_kpp, and general categories</li>";
    echo "<li><strong>Pamong MT:</strong> Can create pamong_mt, wali_x, and general categories</li>";
    echo "<li><strong>Pamong MM:</strong> Can create pamong_mm, wali_xi, and general categories</li>";
    echo "<li><strong>Pamong MU:</strong> Can create pamong_mu, wali_xii, wali_kpa, and general categories</li>";
    echo "<li><strong>Wali Kelas:</strong> Can create wali_* (sesuai tingkat) and general categories</li>";
    echo "<li><strong>BK:</strong> Can create all bk_* categories and general categories</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
