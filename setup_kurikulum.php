<?php
/**
 * Setup Kurikulum System
 */

require_once 'app/config/Database.php';

try {
    $db = new Database();
    
    echo "<h1>🏫 Setup Sistem Kurikulum</h1>";
    echo "<div style='font-family: Arial, sans-serif; margin: 20px;'>";
    
    // Step 1: Create kurikulum table
    echo "<h2>📋 Step 1: Create Kurikulum Table</h2>";
    
    $createTableSQL = "
        CREATE TABLE IF NOT EXISTS kurikulum (
            id_kurikulum INT AUTO_INCREMENT PRIMARY KEY,
            nama_kurikulum VARCHAR(100) NOT NULL,
            kode_kurikulum VARCHAR(20) NOT NULL UNIQUE,
            deskripsi TEXT,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ";
    
    $result = $db->execute($createTableSQL);
    if ($result !== false) {
        echo "<p style='color: green;'>✅ Tabel kurikulum berhasil dibuat</p>";
    } else {
        echo "<p style='color: red;'>❌ Gagal membuat tabel kurikulum</p>";
    }
    
    // Step 2: Insert default kurikulum data
    echo "<h2>📚 Step 2: Insert Default Kurikulum</h2>";
    
    $defaultKurikulum = [
        [
            'nama_kurikulum' => 'Kurikulum Merdeka',
            'kode_kurikulum' => 'MERDEKA',
            'deskripsi' => 'Kurikulum yang memberikan kebebasan belajar kepada siswa untuk mengeksplorasi minat dan bakat mereka dengan pendekatan yang lebih fleksibel dan inovatif.',
            'is_active' => 1
        ],
        [
            'nama_kurikulum' => 'Kurikulum Seminari',
            'kode_kurikulum' => 'SEMINARI',
            'deskripsi' => 'Kurikulum khusus untuk pendidikan seminari dengan fokus pada pembentukan karakter, spiritualitas, dan nilai-nilai keagamaan.',
            'is_active' => 1
        ]
    ];
    
    foreach ($defaultKurikulum as $kurikulum) {
        // Check if already exists
        $existing = $db->fetchOne("SELECT id_kurikulum FROM kurikulum WHERE kode_kurikulum = ?", [$kurikulum['kode_kurikulum']]);
        
        if (!$existing) {
            $result = $db->execute("
                INSERT INTO kurikulum (nama_kurikulum, kode_kurikulum, deskripsi, is_active, created_at)
                VALUES (?, ?, ?, ?, NOW())
            ", [
                $kurikulum['nama_kurikulum'],
                $kurikulum['kode_kurikulum'],
                $kurikulum['deskripsi'],
                $kurikulum['is_active']
            ]);
            
            if ($result) {
                echo "<p style='color: green;'>✅ Added: {$kurikulum['nama_kurikulum']} ({$kurikulum['kode_kurikulum']})</p>";
            } else {
                echo "<p style='color: red;'>❌ Failed to add: {$kurikulum['nama_kurikulum']}</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ Already exists: {$kurikulum['nama_kurikulum']}</p>";
        }
    }
    
    // Step 3: Add kurikulum_id column to kelas table
    echo "<h2>🔧 Step 3: Update Kelas Table Structure</h2>";
    
    // Check if column already exists
    $columns = $db->fetchAll("DESCRIBE kelas");
    $hasKurikulumId = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'kurikulum_id') {
            $hasKurikulumId = true;
            break;
        }
    }
    
    if (!$hasKurikulumId) {
        $result = $db->execute("ALTER TABLE kelas ADD COLUMN kurikulum_id INT DEFAULT NULL AFTER tingkat");
        if ($result !== false) {
            echo "<p style='color: green;'>✅ Added kurikulum_id column to kelas table</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to add kurikulum_id column</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ kurikulum_id column already exists</p>";
    }
    
    // Step 4: Add foreign key constraint
    echo "<h2>🔗 Step 4: Add Foreign Key Constraint</h2>";
    
    try {
        $result = $db->execute("
            ALTER TABLE kelas 
            ADD CONSTRAINT fk_kelas_kurikulum 
            FOREIGN KEY (kurikulum_id) REFERENCES kurikulum(id_kurikulum) 
            ON DELETE SET NULL ON UPDATE CASCADE
        ");
        if ($result !== false) {
            echo "<p style='color: green;'>✅ Foreign key constraint added</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to add foreign key constraint</p>";
        }
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "<p style='color: orange;'>⚠️ Foreign key constraint already exists</p>";
        } else {
            echo "<p style='color: red;'>❌ Error adding foreign key: " . $e->getMessage() . "</p>";
        }
    }
    
    // Step 5: Create indexes
    echo "<h2>📊 Step 5: Create Indexes</h2>";
    
    $indexes = [
        "CREATE INDEX idx_kelas_kurikulum ON kelas(kurikulum_id)",
        "CREATE INDEX idx_kurikulum_active ON kurikulum(is_active)",
        "CREATE INDEX idx_kurikulum_kode ON kurikulum(kode_kurikulum)"
    ];
    
    foreach ($indexes as $indexSQL) {
        try {
            $result = $db->execute($indexSQL);
            if ($result !== false) {
                echo "<p style='color: green;'>✅ Index created successfully</p>";
            }
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                echo "<p style='color: orange;'>⚠️ Index already exists</p>";
            } else {
                echo "<p style='color: red;'>❌ Error creating index: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    // Step 6: Migrate existing data
    echo "<h2>🔄 Step 6: Migrate Existing Data</h2>";
    
    // Set default kurikulum for existing kelas
    $result = $db->execute("UPDATE kelas SET kurikulum_id = 1 WHERE kurikulum_id IS NULL");
    if ($result !== false) {
        $affectedRows = $db->getConnection()->affected_rows;
        echo "<p style='color: green;'>✅ Updated $affectedRows existing kelas with default kurikulum</p>";
    }
    
    // Step 7: Show final status
    echo "<h2>📊 Step 7: Final Status</h2>";
    
    $kurikulumCount = $db->fetchOne("SELECT COUNT(*) as count FROM kurikulum");
    $kelasCount = $db->fetchOne("SELECT COUNT(*) as count FROM kelas");
    $kelasWithKurikulum = $db->fetchOne("SELECT COUNT(*) as count FROM kelas WHERE kurikulum_id IS NOT NULL");
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Item</th><th>Count</th></tr>";
    echo "<tr><td>Total Kurikulum</td><td>{$kurikulumCount['count']}</td></tr>";
    echo "<tr><td>Total Kelas</td><td>{$kelasCount['count']}</td></tr>";
    echo "<tr><td>Kelas with Kurikulum</td><td>{$kelasWithKurikulum['count']}</td></tr>";
    echo "</table>";
    
    echo "<hr>";
    echo "<h2>🎉 Setup Complete!</h2>";
    echo "<p>Sistem kurikulum berhasil disetup. Sekarang Anda dapat:</p>";
    echo "<ul>";
    echo "<li>✅ Mengelola kurikulum melalui menu admin</li>";
    echo "<li>✅ Menggunakan kurikulum saat membuat/edit kelas</li>";
    echo "<li>✅ Melihat statistik penggunaan kurikulum</li>";
    echo "</ul>";
    
    echo "<p>
        <a href='public/kurikulum' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>
            📚 Kelola Kurikulum
        </a>
        <a href='public/kelas' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>
            🏫 Kelola Kelas
        </a>
    </p>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h1 style='color: red;'>❌ Setup Error</h1>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database connection and permissions.</p>";
}
?>
