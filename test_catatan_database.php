<?php
/**
 * Test Catatan Database Setup
 */

require_once 'app/models/Database.php';

echo "<h2>🗂️ Test Catatan Database Setup</h2>";

try {
    $db = new Database();
    
    // Create tables if not exist
    echo "<h3>📋 Creating Tables...</h3>";
    
    // Create catatan_siswa table
    $createCatatanTable = "
    CREATE TABLE IF NOT EXISTS catatan_siswa (
        id INT PRIMARY KEY AUTO_INCREMENT,
        siswa_id INT NOT NULL,
        jenis_catatan ENUM(
            'pamong_mp', 'pamong_mt', 'pamong_mm', 'pamong_mu',
            'wali_kpp', 'wali_x', 'wali_xi', 'wali_xii', 'wali_kpa',
            'bk_konseling', 'bk_pelanggaran', 'bk_prestasi', 'bk_lainnya'
        ) NOT NULL,
        judul_catatan VARCHAR(255) NOT NULL,
        isi_catatan TEXT NOT NULL,
        tanggal_catatan DATE NOT NULL,
        tingkat_prioritas ENUM('rendah', 'sedang', 'tinggi', 'urgent') DEFAULT 'sedang',
        status_catatan ENUM('draft', 'aktif', 'selesai', 'ditunda') DEFAULT 'aktif',
        tindak_lanjut TEXT,
        tanggal_tindak_lanjut DATE,
        created_by INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_by INT,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_siswa_jenis (siswa_id, jenis_catatan),
        INDEX idx_tanggal (tanggal_catatan),
        INDEX idx_status (status_catatan),
        INDEX idx_prioritas (tingkat_prioritas)
    )";
    
    $db->query($createCatatanTable);
    echo "<p style='color: green;'>✅ Table catatan_siswa created/verified</p>";
    
    // Create kategori_catatan table
    $createKategoriTable = "
    CREATE TABLE IF NOT EXISTS kategori_catatan (
        id INT PRIMARY KEY AUTO_INCREMENT,
        kode_kategori VARCHAR(20) NOT NULL UNIQUE,
        nama_kategori VARCHAR(100) NOT NULL,
        deskripsi TEXT,
        warna_badge VARCHAR(7) DEFAULT '#6c757d',
        icon_class VARCHAR(50) DEFAULT 'bi-note-text',
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    $db->query($createKategoriTable);
    echo "<p style='color: green;'>✅ Table kategori_catatan created/verified</p>";
    
    // Insert default categories
    echo "<h3>📝 Inserting Default Categories...</h3>";
    
    $categories = [
        // Pamong
        ['pamong_mp', 'Pamong MP', 'Catatan Pamong Masa Persiapan', '#17a2b8', 'bi-person-badge'],
        ['pamong_mt', 'Pamong MT', 'Catatan Pamong Masa Transisi', '#28a745', 'bi-person-check'],
        ['pamong_mm', 'Pamong MM', 'Catatan Pamong Masa Mandiri', '#ffc107', 'bi-person-gear'],
        ['pamong_mu', 'Pamong MU', 'Catatan Pamong Masa Uji', '#fd7e14', 'bi-person-exclamation'],
        
        // Wali Kelas
        ['wali_kpp', 'Wali Kelas KPP', 'Catatan Wali Kelas Persiapan Profesi', '#6f42c1', 'bi-mortarboard'],
        ['wali_x', 'Wali Kelas X', 'Catatan Wali Kelas X', '#007bff', 'bi-book'],
        ['wali_xi', 'Wali Kelas XI', 'Catatan Wali Kelas XI', '#0d6efd', 'bi-journal'],
        ['wali_xii', 'Wali Kelas XII', 'Catatan Wali Kelas XII', '#6610f2', 'bi-graduation'],
        ['wali_kpa', 'Wali Kelas KPA', 'Catatan Wali Kelas Pasca', '#e83e8c', 'bi-award'],
        
        // BK
        ['bk_konseling', 'BK Konseling', 'Catatan Bimbingan Konseling', '#dc3545', 'bi-heart'],
        ['bk_pelanggaran', 'BK Pelanggaran', 'Catatan Pelanggaran Siswa', '#dc3545', 'bi-exclamation-triangle'],
        ['bk_prestasi', 'BK Prestasi', 'Catatan Prestasi Siswa', '#198754', 'bi-trophy'],
        ['bk_lainnya', 'BK Lainnya', 'Catatan BK Lainnya', '#6c757d', 'bi-chat-dots']
    ];
    
    foreach ($categories as $cat) {
        try {
            $db->query("INSERT IGNORE INTO kategori_catatan (kode_kategori, nama_kategori, deskripsi, warna_badge, icon_class) VALUES (?, ?, ?, ?, ?)", $cat);
            echo "<p style='color: green;'>✅ Category: {$cat[1]}</p>";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Category {$cat[1]} already exists</p>";
        }
    }
    
    // Insert sample catatan
    echo "<h3>📄 Inserting Sample Catatan...</h3>";
    
    $sampleCatatan = [
        [
            1, // siswa_id
            'pamong_mp',
            'Evaluasi Awal Masa Persiapan',
            'Siswa menunjukkan antusiasme yang baik dalam mengikuti program masa persiapan. Perlu bimbingan lebih dalam hal kedisiplinan waktu.',
            '2024-01-15',
            'sedang',
            'aktif',
            'Jadwalkan sesi konseling individual untuk membahas manajemen waktu',
            '2024-01-22',
            1
        ],
        [
            1, // siswa_id
            'wali_x',
            'Perkembangan Akademik Semester 1',
            'Prestasi akademik siswa cukup baik dengan rata-rata nilai 8.2. Namun perlu peningkatan di mata pelajaran Matematika.',
            '2024-01-10',
            'tinggi',
            'aktif',
            'Koordinasi dengan guru Matematika untuk program remedial',
            '2024-01-17',
            1
        ],
        [
            1, // siswa_id
            'bk_konseling',
            'Konseling Adaptasi Lingkungan Sekolah',
            'Siswa mengalami kesulitan adaptasi dengan lingkungan baru. Sudah dilakukan 2 sesi konseling dengan progress yang positif.',
            '2024-01-08',
            'sedang',
            'selesai',
            null,
            null,
            1
        ]
    ];
    
    foreach ($sampleCatatan as $catatan) {
        try {
            $db->query("INSERT INTO catatan_siswa (siswa_id, jenis_catatan, judul_catatan, isi_catatan, tanggal_catatan, tingkat_prioritas, status_catatan, tindak_lanjut, tanggal_tindak_lanjut, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", $catatan);
            echo "<p style='color: green;'>✅ Sample catatan: {$catatan[2]}</p>";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Sample catatan already exists or error: " . $e->getMessage() . "</p>";
        }
    }
    
    // Test queries
    echo "<h3>🔍 Testing Queries...</h3>";
    
    // Test categories
    $categories = $db->fetchAll("SELECT * FROM kategori_catatan WHERE is_active = 1");
    echo "<p><strong>Categories found:</strong> " . count($categories) . "</p>";
    
    // Test catatan
    $catatan = $db->fetchAll("SELECT * FROM catatan_siswa WHERE siswa_id = 1");
    echo "<p><strong>Catatan for siswa ID 1:</strong> " . count($catatan) . "</p>";
    
    // Test join query
    $joinResult = $db->fetchAll("
        SELECT c.*, k.nama_kategori, k.warna_badge, k.icon_class
        FROM catatan_siswa c
        LEFT JOIN kategori_catatan k ON c.jenis_catatan = k.kode_kategori
        WHERE c.siswa_id = 1
        ORDER BY c.tanggal_catatan DESC
    ");
    
    echo "<p><strong>Join query result:</strong> " . count($joinResult) . " records</p>";
    
    if (!empty($joinResult)) {
        echo "<h4>Sample Catatan Data:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>ID</th><th>Kategori</th><th>Judul</th><th>Tanggal</th><th>Prioritas</th><th>Status</th>";
        echo "</tr>";
        
        foreach ($joinResult as $row) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td><span style='color: {$row['warna_badge']};'>{$row['nama_kategori']}</span></td>";
            echo "<td>{$row['judul_catatan']}</td>";
            echo "<td>{$row['tanggal_catatan']}</td>";
            echo "<td>{$row['tingkat_prioritas']}</td>";
            echo "<td>{$row['status_catatan']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>✅ Database Setup Complete!</h3>";
    echo "<p style='color: green; font-weight: bold;'>🎉 Catatan system is ready to use!</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><a href='public/siswa/detail/1'>👤 Test Catatan di Detail Siswa</a></p>";
echo "<p><a href='public/siswa'>📋 Student List</a></p>";
?>
