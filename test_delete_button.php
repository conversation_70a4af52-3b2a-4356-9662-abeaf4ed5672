<?php
/**
 * Test Delete Button Functionality
 */

require_once 'app/helpers/Security.php';
require_once 'app/helpers/SimpleSessionManager.php';

$sessionManager = new SimpleSessionManager();

// Check authentication
if (!Security::isAuthenticated()) {
    echo "<p style='color: red;'>❌ Not authenticated. <a href='simple_login.php'>Please login first</a></p>";
    exit;
}

$currentUser = $sessionManager->getCurrentUser();
$userRole = $currentUser['role'] ?? '';
$canManage = ($userRole === 'admin');

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Delete Button</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container mt-4">
        <h1>🧪 Test Delete Button Functionality</h1>
        
        <div class="alert alert-info">
            <h5>Current User Info:</h5>
            <ul>
                <li><strong>Username:</strong> <?= htmlspecialchars($currentUser['username'] ?? 'N/A') ?></li>
                <li><strong>Role:</strong> <?= htmlspecialchars($userRole) ?></li>
                <li><strong>Can Manage:</strong> <?= $canManage ? '✅ YES' : '❌ NO' ?></li>
            </ul>
        </div>

        <?php if ($canManage): ?>
        <div class="alert alert-success">
            <h5>✅ You have admin permissions!</h5>
            <p>The delete button should be visible and functional.</p>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>Test Delete Button</h5>
            </div>
            <div class="card-body">
                <p>Click the button below to test the delete functionality:</p>
                
                <button type="button" 
                        class="btn btn-outline-danger"
                        onclick="confirmDelete(1, 'Test Kelas')"
                        title="Test Delete">
                    <i class="bi bi-trash"></i> Test Delete Button
                </button>
                
                <hr>
                
                <h6>Direct Function Tests:</h6>
                <button type="button" class="btn btn-primary" onclick="testConfirmDelete()">
                    Test confirmDelete() Function
                </button>
                <button type="button" class="btn btn-warning" onclick="testExecuteDelete()">
                    Test executeDelete() Function
                </button>
            </div>
        </div>
        
        <?php else: ?>
        <div class="alert alert-warning">
            <h5>⚠️ No admin permissions</h5>
            <p>You need admin role to see delete buttons. Current role: <strong><?= htmlspecialchars($userRole) ?></strong></p>
        </div>
        <?php endif; ?>
        
        <div class="mt-4">
            <a href="public/kelas" class="btn btn-primary">🏫 Go to Kelas Management</a>
            <a href="debug_user_role.php" class="btn btn-info">🔍 Debug User Role</a>
        </div>
    </div>

    <script>
    // Test functions
    function testConfirmDelete() {
        console.log('Testing confirmDelete function...');
        if (typeof confirmDelete === 'function') {
            console.log('✅ confirmDelete function exists');
            confirmDelete(999, 'Test Kelas Function');
        } else {
            console.error('❌ confirmDelete function not found');
            alert('❌ confirmDelete function not found');
        }
    }
    
    function testExecuteDelete() {
        console.log('Testing executeDelete function...');
        if (typeof executeDelete === 'function') {
            console.log('✅ executeDelete function exists');
            // Don't actually execute, just test if function exists
            alert('✅ executeDelete function exists (not executed for safety)');
        } else {
            console.error('❌ executeDelete function not found');
            alert('❌ executeDelete function not found');
        }
    }

    // Copy the delete functions from kelas/list.php for testing
    function confirmDelete(id, namaKelas) {
        console.log('confirmDelete called with:', id, namaKelas);
        
        const modal = `
            <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title">
                                <i class="bi bi-exclamation-triangle"></i>
                                Konfirmasi Hapus Kelas
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="text-center mb-3">
                                <i class="bi bi-building display-1 text-danger"></i>
                            </div>
                            <p class="text-center">
                                Apakah Anda yakin ingin menghapus kelas <strong>"${namaKelas}"</strong>?
                            </p>
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle"></i>
                                <strong>Peringatan:</strong> Tindakan ini tidak dapat dibatalkan.
                                Semua data yang terkait dengan kelas ini akan ikut terhapus.
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle"></i> Batal
                            </button>
                            <button type="button" class="btn btn-danger" onclick="executeDelete(${id})">
                                <i class="bi bi-trash"></i> Ya, Hapus Kelas
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('deleteModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modal);

        // Show modal using Bootstrap 5
        const modalElement = document.getElementById('deleteModal');
        const bootstrapModal = new bootstrap.Modal(modalElement);
        bootstrapModal.show();
    }

    function executeDelete(id) {
        console.log('executeDelete called with:', id);
        
        // Hide modal
        const modalElement = document.getElementById('deleteModal');
        if (modalElement) {
            const bootstrapModal = bootstrap.Modal.getInstance(modalElement);
            if (bootstrapModal) {
                bootstrapModal.hide();
            }
        }

        // For testing, just show alert instead of actual delete
        alert(`Would delete kelas with ID: ${id}\n\nURL: /siswa-app/public/kelas/delete/${id}`);
        
        // Uncomment below for actual delete
        /*
        // Create form for POST request with CSRF token
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/siswa-app/public/kelas/delete/' + id;
        
        // Add CSRF token
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = '<?= Security::generateCSRFToken() ?>';
        form.appendChild(csrfInput);
        
        // Add to body and submit
        document.body.appendChild(form);
        form.submit();
        */
    }

    // Log when page loads
    console.log('Test page loaded');
    console.log('User role:', '<?= $userRole ?>');
    console.log('Can manage:', <?= $canManage ? 'true' : 'false' ?>);
    </script>
</body>
</html>
