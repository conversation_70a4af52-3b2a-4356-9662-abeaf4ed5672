<?php
/**
 * Test Delete Kelas Functionality
 */

require_once 'app/config/Database.php';
require_once 'app/models/Kelas.php';
require_once 'app/helpers/Security.php';
require_once 'app/helpers/SimpleSessionManager.php';

$sessionManager = new SimpleSessionManager();

// Check authentication
if (!Security::isAuthenticated()) {
    echo "<p style='color: red;'>❌ Not authenticated. <a href='simple_login.php'>Please login first</a></p>";
    exit;
}

$currentUser = $sessionManager->getCurrentUser();
$kelasModel = new Kelas();

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Delete Kelas Functionality</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .btn { padding: 8px 16px; margin: 5px; text-decoration: none; border-radius: 4px; display: inline-block; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-primary { background: #007bff; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
    </style>
</head>
<body>
    <h1>🧪 Test Delete Kelas Functionality</h1>
    
    <div class="test-section">
        <h2>👤 Current User Info</h2>
        <table>
            <tr><th>Property</th><th>Value</th></tr>
            <tr><td>User ID</td><td><?= htmlspecialchars($currentUser['id'] ?? 'N/A') ?></td></tr>
            <tr><td>Username</td><td><?= htmlspecialchars($currentUser['username'] ?? 'N/A') ?></td></tr>
            <tr><td>Role</td><td><?= htmlspecialchars($currentUser['role'] ?? 'N/A') ?></td></tr>
        </table>
    </div>

    <div class="test-section">
        <h2>🏫 Available Kelas for Testing</h2>
        <?php
        $allKelas = $kelasModel->getAll();
        if (empty($allKelas)) {
            echo "<p class='error'>❌ No kelas found in database</p>";
        } else {
            echo "<table>";
            echo "<tr><th>ID</th><th>Nama Kelas</th><th>Tingkat</th><th>Kurikulum</th><th>Status</th><th>Actions</th></tr>";
            foreach ($allKelas as $k) {
                $status = $k['is_active'] ? '<span class="success">✅ Active</span>' : '<span class="error">❌ Inactive</span>';
                $kurikulum = $k['nama_kurikulum'] ?? 'No Kurikulum';
                
                echo "<tr>";
                echo "<td>" . htmlspecialchars($k['id_kelas'] ?? '') . "</td>";
                echo "<td>" . htmlspecialchars($k['nama_kelas'] ?? '') . "</td>";
                echo "<td>" . htmlspecialchars($k['tingkat'] ?? '') . "</td>";
                echo "<td>" . htmlspecialchars($kurikulum) . "</td>";
                echo "<td>$status</td>";
                echo "<td>";
                
                if ($currentUser['role'] === 'admin') {
                    echo "<a href='/siswa-app/public/kelas/delete/{$k['id_kelas']}' class='btn btn-danger' onclick='return confirm(\"Test delete kelas {$k['nama_kelas']}?\")'>🗑️ Test Delete</a>";
                } else {
                    echo "<span class='warning'>⚠️ No permission</span>";
                }
                
                echo "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        ?>
    </div>

    <div class="test-section">
        <h2>🔧 Permission Test</h2>
        <?php
        $userRole = $currentUser['role'] ?? '';
        
        echo "<h3>Testing canManageKelas() function:</h3>";
        
        // Simulate the canManageKelas function
        function canManageKelas($role) {
            return $role === 'admin';
        }
        
        $canManage = canManageKelas($userRole);
        
        echo "<table>";
        echo "<tr><th>Test</th><th>Result</th></tr>";
        echo "<tr><td>Current Role</td><td><strong>" . htmlspecialchars($userRole) . "</strong></td></tr>";
        echo "<tr><td>Can Manage Kelas</td><td>" . ($canManage ? '<span class="success">✅ YES</span>' : '<span class="error">❌ NO</span>') . "</td></tr>";
        echo "<tr><td>Can Delete Kelas</td><td>" . ($canManage ? '<span class="success">✅ YES</span>' : '<span class="error">❌ NO</span>') . "</td></tr>";
        echo "</table>";
        
        if (!$canManage) {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<strong>⚠️ Permission Issue:</strong> Current user role '$userRole' does not have permission to delete kelas. Only 'admin' role can delete kelas.";
            echo "</div>";
        }
        ?>
    </div>

    <div class="test-section">
        <h2>🔍 Database Connection Test</h2>
        <?php
        try {
            $db = new Database();
            echo "<p class='success'>✅ Database connection successful</p>";
            
            // Test if we can query kelas table
            $testQuery = $db->fetchOne("SELECT COUNT(*) as count FROM kelas");
            echo "<p class='success'>✅ Can query kelas table: {$testQuery['count']} records found</p>";
            
            // Test if we can query siswa table (for delete validation)
            try {
                $testSiswa = $db->fetchOne("SELECT COUNT(*) as count FROM siswa");
                echo "<p class='success'>✅ Can query siswa table: {$testSiswa['count']} records found</p>";
            } catch (Exception $e) {
                echo "<p class='warning'>⚠️ Cannot query siswa table: " . $e->getMessage() . "</p>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ Database connection failed: " . $e->getMessage() . "</p>";
        }
        ?>
    </div>

    <div class="test-section">
        <h2>🧪 Delete Method Test</h2>
        <?php
        if ($currentUser['role'] === 'admin' && !empty($allKelas)) {
            echo "<p class='info'>ℹ️ As admin, you can test the delete functionality:</p>";
            echo "<ol>";
            echo "<li>Click on 'Test Delete' button for any kelas above</li>";
            echo "<li>You should be redirected to a confirmation page</li>";
            echo "<li>The confirmation page should show kelas details</li>";
            echo "<li>After confirming, the kelas should be soft-deleted (is_active = 0)</li>";
            echo "</ol>";
            
            echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<strong>💡 Note:</strong> This is a soft delete - the kelas will be marked as inactive (is_active = 0) but not permanently removed from database.";
            echo "</div>";
        } else {
            echo "<p class='warning'>⚠️ You need admin role to test delete functionality</p>";
        }
        ?>
    </div>

    <div class="test-section">
        <h2>📋 Expected Behavior</h2>
        <h3>✅ For Admin Users:</h3>
        <ul>
            <li>Should see delete buttons in kelas list</li>
            <li>Clicking delete should redirect to confirmation page</li>
            <li>Confirmation page should show kelas details</li>
            <li>After confirming, kelas should be soft-deleted</li>
            <li>Success message should be shown</li>
        </ul>
        
        <h3>❌ For Non-Admin Users:</h3>
        <ul>
            <li>Should NOT see delete buttons</li>
            <li>Direct access to delete URL should be blocked</li>
            <li>Should get "no permission" error message</li>
        </ul>
        
        <h3>🛡️ Security Checks:</h3>
        <ul>
            <li>CSRF token validation</li>
            <li>Role-based access control</li>
            <li>Check for students in kelas before delete</li>
            <li>Proper error handling</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🔧 Quick Actions</h2>
        <p>
            <a href="public/kelas" class="btn btn-primary">🏫 Go to Kelas Management</a>
            <a href="simple_login.php?logout=1" class="btn btn-secondary">🚪 Logout</a>
        </p>
    </div>

    <script>
    // Auto-refresh every 30 seconds to see updated data
    setTimeout(() => {
        location.reload();
    }, 30000);
    </script>

</body>
</html>
