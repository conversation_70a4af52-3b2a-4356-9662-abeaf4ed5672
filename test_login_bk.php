<?php
/**
 * Test login as BK for testing catatan functionality
 */

session_start();

// Set session as BK
$_SESSION['user_id'] = 4;
$_SESSION['username'] = 'bk_konselor';
$_SESSION['user_role'] = 'bk';
$_SESSION['nama_lengkap'] = 'BK Konselor Test';

echo "<h2>✅ Login Test sebagai BK (Bimbingan Konseling)</h2>";
echo "<p><strong>User ID:</strong> " . $_SESSION['user_id'] . "</p>";
echo "<p><strong>Username:</strong> " . $_SESSION['username'] . "</p>";
echo "<p><strong>Role:</strong> " . $_SESSION['user_role'] . "</p>";
echo "<p><strong>Nama:</strong> " . $_SESSION['nama_lengkap'] . "</p>";

echo "<h3>🔗 Test Links:</h3>";
echo "<ul>";
echo "<li><a href='/siswa-app/public/siswa'>Daftar Siswa</a></li>";
echo "<li><a href='/siswa-app/public/siswa/detail/1'>Detail Siswa #1</a></li>";
echo "<li><a href='/siswa-app/public/catatan/add/1'>Tambah Catatan Siswa #1</a></li>";
echo "<li><a href='/siswa-app/public/dashboard'>Dashboard</a></li>";
echo "</ul>";

echo "<h3>📋 Akses yang Diharapkan:</h3>";
echo "<ul>";
echo "<li>✅ Dapat melihat semua siswa</li>";
echo "<li>✅ Dapat membuat semua catatan BK (konseling, pelanggaran, prestasi, lainnya)</li>";
echo "<li>✅ Dapat membuat catatan umum (akademik, prestasi, dll)</li>";
echo "<li>❌ Tidak dapat membuat catatan pamong</li>";
echo "<li>❌ Tidak dapat membuat catatan wali kelas</li>";
echo "</ul>";

// Test submit catatan BK
echo "<hr>";
echo "<h3>🧪 Test Submit Catatan BK</h3>";

require_once 'app/controllers/CatatanController.php';
require_once 'app/helpers/Security.php';

// Test multiple BK catatan types
$bkCatatanTypes = [
    [
        'jenis' => 'bk_konseling',
        'judul' => 'Sesi Konseling Individual',
        'isi' => 'Siswa mengalami kesulitan adaptasi dengan lingkungan sekolah baru. Telah dilakukan sesi konseling untuk membantu proses adaptasi dan membangun kepercayaan diri.'
    ],
    [
        'jenis' => 'bk_pelanggaran',
        'judul' => 'Pelanggaran Tata Tertib',
        'isi' => 'Siswa terlambat masuk kelas sebanyak 3 kali dalam seminggu. Telah diberikan teguran dan konseling tentang pentingnya disiplin waktu.'
    ],
    [
        'jenis' => 'bk_prestasi',
        'judul' => 'Prestasi Lomba Debat',
        'isi' => 'Siswa meraih juara 2 dalam lomba debat tingkat kabupaten. Menunjukkan kemampuan komunikasi dan analisis yang baik.'
    ]
];

foreach ($bkCatatanTypes as $index => $catatan) {
    echo "<h4>📝 Test " . ($index + 1) . ": " . $catatan['judul'] . "</h4>";
    
    $_POST = [
        'csrf_token' => Security::generateCSRFToken(),
        'siswa_id' => '1',
        'jenis_catatan' => $catatan['jenis'],
        'judul_catatan' => $catatan['judul'],
        'isi_catatan' => $catatan['isi'],
        'tanggal_catatan' => date('Y-m-d'),
        'tingkat_prioritas' => 'sedang',
        'status_catatan' => 'aktif',
        'tindak_lanjut' => 'Follow up sesuai kebutuhan',
        'tanggal_tindak_lanjut' => date('Y-m-d', strtotime('+1 week'))
    ];

    $_SERVER['REQUEST_METHOD'] = 'POST';

    try {
        $controller = new CatatanController();
        
        // Capture output
        ob_start();
        $controller->create();
        $output = ob_get_clean();
        
        if (empty($output)) {
            echo "<p style='color: green;'>✅ Catatan BK ({$catatan['jenis']}) berhasil dibuat!</p>";
        } else {
            echo "<div style='background: #f8f9fa; padding: 1rem; border-radius: 5px;'>";
            echo $output;
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    }
}

// Show all catatan for student
echo "<hr>";
echo "<h3>📊 Semua Catatan untuk Siswa #1:</h3>";

try {
    require_once 'app/models/CatatanSiswa.php';
    $catatanModel = new CatatanSiswa();
    $allCatatan = $catatanModel->getBySiswaId(1);
    
    if (!empty($allCatatan)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin-top: 1rem;'>";
        echo "<tr style='background: #f8f9fa;'><th>ID</th><th>Jenis</th><th>Judul</th><th>Tanggal</th><th>Status</th><th>Dibuat Oleh</th></tr>";
        foreach ($allCatatan as $catatan) {
            echo "<tr>";
            echo "<td>" . ($catatan['id'] ?? 'N/A') . "</td>";
            echo "<td><span style='background: #e9ecef; padding: 2px 6px; border-radius: 3px;'>" . ($catatan['jenis_catatan'] ?? 'N/A') . "</span></td>";
            echo "<td>" . ($catatan['judul'] ?? $catatan['judul_catatan'] ?? 'N/A') . "</td>";
            echo "<td>" . ($catatan['tanggal'] ?? $catatan['tanggal_catatan'] ?? 'N/A') . "</td>";
            echo "<td>" . ($catatan['status_catatan'] ?? 'N/A') . "</td>";
            echo "<td>" . ($catatan['created_by_name'] ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p><strong>Total catatan:</strong> " . count($allCatatan) . "</p>";
    } else {
        echo "<p>Tidak ada catatan ditemukan.</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error mengambil catatan: " . $e->getMessage() . "</p>";
}
?>
