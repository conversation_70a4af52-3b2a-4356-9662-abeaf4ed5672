<?php
/**
 * Test login as pamong for testing catatan functionality
 */

session_start();

// Set session as pamong MP
$_SESSION['user_id'] = 2;
$_SESSION['username'] = 'pamong_mp';
$_SESSION['user_role'] = 'pamong';
$_SESSION['pamong_tingkat'] = 'MP';  // MP untuk KPP
$_SESSION['nama_lengkap'] = 'Pamong MP Test';

echo "<h2>✅ Login Test sebagai Pamong MP</h2>";
echo "<p><strong>User ID:</strong> " . $_SESSION['user_id'] . "</p>";
echo "<p><strong>Username:</strong> " . $_SESSION['username'] . "</p>";
echo "<p><strong>Role:</strong> " . $_SESSION['user_role'] . "</p>";
echo "<p><strong>Pamong Tingkat:</strong> " . $_SESSION['pamong_tingkat'] . "</p>";
echo "<p><strong>Nama:</strong> " . $_SESSION['nama_lengkap'] . "</p>";

echo "<h3>🔗 Test Links:</h3>";
echo "<ul>";
echo "<li><a href='/siswa-app/public/siswa'>Daftar Siswa</a></li>";
echo "<li><a href='/siswa-app/public/siswa/detail/1'>Detail Siswa #1</a></li>";
echo "<li><a href='/siswa-app/public/catatan/add/1'>Tambah Catatan Siswa #1</a></li>";
echo "<li><a href='/siswa-app/public/dashboard'>Dashboard</a></li>";
echo "</ul>";

echo "<h3>📋 Akses yang Diharapkan:</h3>";
echo "<ul>";
echo "<li>✅ Dapat melihat siswa tingkat KPP</li>";
echo "<li>✅ Dapat membuat catatan pamong_mp</li>";
echo "<li>✅ Dapat membuat catatan wali_kpp</li>";
echo "<li>✅ Dapat membuat catatan umum (akademik, prestasi, dll)</li>";
echo "<li>❌ Tidak dapat membuat catatan pamong tingkat lain</li>";
echo "<li>❌ Tidak dapat membuat catatan BK</li>";
echo "</ul>";
?>
