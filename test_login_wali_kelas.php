<?php
/**
 * Test login as wali kelas for testing catatan functionality
 */

session_start();

// Set session as wali kelas
$_SESSION['user_id'] = 3;
$_SESSION['username'] = 'wali_kelas_x';
$_SESSION['user_role'] = 'wali_kelas';
$_SESSION['wali_kelas_tingkat'] = 'X';  // Wali kelas untuk tingkat X
$_SESSION['nama_lengkap'] = 'Wali Kelas X Test';

echo "<h2>✅ Login Test sebagai Wali Kelas X</h2>";
echo "<p><strong>User ID:</strong> " . $_SESSION['user_id'] . "</p>";
echo "<p><strong>Username:</strong> " . $_SESSION['username'] . "</p>";
echo "<p><strong>Role:</strong> " . $_SESSION['user_role'] . "</p>";
echo "<p><strong>Wali Kelas Tingkat:</strong> " . $_SESSION['wali_kelas_tingkat'] . "</p>";
echo "<p><strong>Nama:</strong> " . $_SESSION['nama_lengkap'] . "</p>";

echo "<h3>🔗 Test Links:</h3>";
echo "<ul>";
echo "<li><a href='/siswa-app/public/siswa'>Daftar Siswa</a></li>";
echo "<li><a href='/siswa-app/public/siswa/detail/1'>Detail Siswa #1</a></li>";
echo "<li><a href='/siswa-app/public/catatan/add/1'>Tambah Catatan Siswa #1</a></li>";
echo "<li><a href='/siswa-app/public/dashboard'>Dashboard</a></li>";
echo "</ul>";

echo "<h3>📋 Akses yang Diharapkan:</h3>";
echo "<ul>";
echo "<li>✅ Dapat melihat siswa di kelas yang diajar</li>";
echo "<li>✅ Dapat membuat catatan wali_x</li>";
echo "<li>✅ Dapat membuat catatan umum (akademik, prestasi, dll)</li>";
echo "<li>❌ Tidak dapat membuat catatan pamong</li>";
echo "<li>❌ Tidak dapat membuat catatan wali tingkat lain</li>";
echo "<li>❌ Tidak dapat membuat catatan BK</li>";
echo "</ul>";

// Test submit catatan wali kelas
echo "<hr>";
echo "<h3>🧪 Test Submit Catatan Wali Kelas</h3>";

require_once 'app/controllers/CatatanController.php';
require_once 'app/helpers/Security.php';

// Simulate POST data for wali kelas
$_POST = [
    'csrf_token' => Security::generateCSRFToken(),
    'siswa_id' => '1',
    'jenis_catatan' => 'wali_x',
    'judul_catatan' => 'Test Catatan Wali Kelas X',
    'isi_catatan' => 'Ini adalah test catatan dari Wali Kelas X untuk siswa. Catatan ini mencakup observasi perilaku dan prestasi akademik siswa di kelas.',
    'tanggal_catatan' => date('Y-m-d'),
    'tingkat_prioritas' => 'sedang',
    'status_catatan' => 'aktif',
    'tindak_lanjut' => 'Konsultasi dengan orang tua dan monitoring khusus',
    'tanggal_tindak_lanjut' => date('Y-m-d', strtotime('+3 days'))
];

$_SERVER['REQUEST_METHOD'] = 'POST';

try {
    $controller = new CatatanController();
    
    echo "<p>📝 Menjalankan create method untuk wali kelas...</p>";
    
    // Capture output
    ob_start();
    $controller->create();
    $output = ob_get_clean();
    
    if (empty($output)) {
        echo "<p style='color: green;'>✅ Catatan wali kelas berhasil dibuat!</p>";
    } else {
        echo "<div style='background: #f8f9fa; padding: 1rem; border-radius: 5px;'>";
        echo $output;
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
