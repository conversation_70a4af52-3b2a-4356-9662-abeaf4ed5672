<?php
/**
 * Test submit catatan functionality
 */

session_start();

// Ensure we're logged in as pamong
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 2;
    $_SESSION['username'] = 'pamong_mp';
    $_SESSION['user_role'] = 'pamong';
    $_SESSION['pamong_tingkat'] = 'MP';
    $_SESSION['nama_lengkap'] = 'Pamong MP Test';
}

require_once 'app/controllers/CatatanController.php';
require_once 'app/helpers/Security.php';

// Simulate POST data
$_POST = [
    'csrf_token' => Security::generateCSRFToken(),
    'siswa_id' => '1',
    'jenis_catatan' => 'pamong_mp',
    'judul_catatan' => 'Test Catatan Pamong MP',
    'isi_catatan' => 'Ini adalah test catatan dari <PERSON> MP untuk siswa. Catatan ini dibuat untuk menguji fungsi tambah catatan dengan role-based access.',
    'tanggal_catatan' => date('Y-m-d'),
    'tingkat_prioritas' => 'sedang',
    'status_catatan' => 'aktif',
    'tindak_lanjut' => 'Monitoring berkala dan evaluasi progress siswa',
    'tanggal_tindak_lanjut' => date('Y-m-d', strtotime('+1 week'))
];

$_SERVER['REQUEST_METHOD'] = 'POST';

echo "<h2>🧪 Test Submit Catatan</h2>";
echo "<h3>📝 Data yang akan disubmit:</h3>";
echo "<pre>";
print_r($_POST);
echo "</pre>";

try {
    $controller = new CatatanController();
    
    echo "<h3>🚀 Menjalankan create method...</h3>";
    
    // Capture output
    ob_start();
    $controller->create();
    $output = ob_get_clean();
    
    echo "<h3>✅ Hasil:</h3>";
    if (empty($output)) {
        echo "<p style='color: green;'>✅ Catatan berhasil dibuat! (Redirect terjadi)</p>";
    } else {
        echo "<div style='background: #f8f9fa; padding: 1rem; border-radius: 5px;'>";
        echo $output;
        echo "</div>";
    }
    
    // Check if catatan was created
    require_once 'app/models/CatatanSiswa.php';
    $catatanModel = new CatatanSiswa();
    $latestCatatan = $catatanModel->getBySiswaId(1);
    
    echo "<h3>📊 Catatan Terbaru untuk Siswa #1:</h3>";
    if (!empty($latestCatatan)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Jenis</th><th>Judul</th><th>Tanggal</th><th>Status</th></tr>";
        foreach (array_slice($latestCatatan, 0, 5) as $catatan) {
            echo "<tr>";
            echo "<td>" . ($catatan['id'] ?? 'N/A') . "</td>";
            echo "<td>" . ($catatan['jenis_catatan'] ?? 'N/A') . "</td>";
            echo "<td>" . ($catatan['judul'] ?? $catatan['judul_catatan'] ?? 'N/A') . "</td>";
            echo "<td>" . ($catatan['tanggal'] ?? $catatan['tanggal_catatan'] ?? 'N/A') . "</td>";
            echo "<td>" . ($catatan['status_catatan'] ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>Tidak ada catatan ditemukan.</p>";
    }
    
} catch (Exception $e) {
    echo "<h3>❌ Error:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h3>🔗 Test Links:</h3>";
echo "<ul>";
echo "<li><a href='/siswa-app/public/siswa/detail/1'>Lihat Detail Siswa #1</a></li>";
echo "<li><a href='/siswa-app/public/catatan/add/1'>Tambah Catatan Lagi</a></li>";
echo "<li><a href='/siswa-app/public/catatan/1'>Lihat Semua Catatan Siswa #1</a></li>";
echo "</ul>";
?>
