<?php
/**
 * Update Existing Kelas to Use Kurikulum
 */

require_once 'app/config/Database.php';

try {
    $db = new Database();
    
    echo "<h1>🔄 Update Kelas dengan Kurikulum</h1>";
    echo "<div style='font-family: Arial, sans-serif; margin: 20px;'>";
    
    // Step 1: Check current status
    echo "<h2>📊 Step 1: Current Status</h2>";
    
    $totalKelas = $db->fetchOne("SELECT COUNT(*) as count FROM kelas");
    $kelasWithKurikulum = $db->fetchOne("SELECT COUNT(*) as count FROM kelas WHERE kurikulum_id IS NOT NULL");
    $kelasWithoutKurikulum = $db->fetchOne("SELECT COUNT(*) as count FROM kelas WHERE kurikulum_id IS NULL");
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr><th>Status</th><th>Count</th></tr>";
    echo "<tr><td>Total Kelas</td><td>{$totalKelas['count']}</td></tr>";
    echo "<tr><td>Kelas dengan Kurikulum</td><td>{$kelasWithKurikulum['count']}</td></tr>";
    echo "<tr><td>Kelas tanpa Kurikulum</td><td>{$kelasWithoutKurikulum['count']}</td></tr>";
    echo "</table>";
    
    // Step 2: Get available kurikulum
    echo "<h2>📚 Step 2: Available Kurikulum</h2>";
    
    $kurikulumList = $db->fetchAll("SELECT * FROM kurikulum ORDER BY nama_kurikulum");
    
    if (empty($kurikulumList)) {
        echo "<p style='color: red;'>❌ No kurikulum found. Please run setup_kurikulum.php first.</p>";
        echo "</div>";
        exit;
    }
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Nama</th><th>Kode</th><th>Status</th></tr>";
    foreach ($kurikulumList as $k) {
        $status = $k['is_active'] ? 'Aktif' : 'Non-aktif';
        echo "<tr>";
        echo "<td>{$k['id_kurikulum']}</td>";
        echo "<td>{$k['nama_kurikulum']}</td>";
        echo "<td>{$k['kode_kurikulum']}</td>";
        echo "<td>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Step 3: Update kelas without kurikulum
    if ($kelasWithoutKurikulum['count'] > 0) {
        echo "<h2>🔄 Step 3: Update Kelas tanpa Kurikulum</h2>";
        
        // Get default kurikulum (Kurikulum Merdeka)
        $defaultKurikulum = $db->fetchOne("SELECT id_kurikulum FROM kurikulum WHERE kode_kurikulum = 'MERDEKA'");
        
        if ($defaultKurikulum) {
            $defaultId = $defaultKurikulum['id_kurikulum'];
            
            // Update kelas without kurikulum to use default
            $result = $db->execute("UPDATE kelas SET kurikulum_id = ? WHERE kurikulum_id IS NULL", [$defaultId]);
            
            if ($result !== false) {
                $affectedRows = $db->getConnection()->affected_rows;
                echo "<p style='color: green;'>✅ Updated $affectedRows kelas to use Kurikulum Merdeka as default</p>";
            } else {
                echo "<p style='color: red;'>❌ Failed to update kelas</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ Default kurikulum (MERDEKA) not found</p>";
        }
    } else {
        echo "<h2>✅ Step 3: All Kelas Already Have Kurikulum</h2>";
        echo "<p style='color: green;'>All kelas already have kurikulum assigned.</p>";
    }
    
    // Step 4: Show kelas with their kurikulum
    echo "<h2>📋 Step 4: Kelas dengan Kurikulum</h2>";
    
    $kelasWithKurikulumData = $db->fetchAll("
        SELECT k.id as id_kelas, k.nama_kelas, k.tingkat, k.tahun_pelajaran, k.wali_kelas,
               kr.nama_kurikulum, kr.kode_kurikulum
        FROM kelas k
        LEFT JOIN kurikulum kr ON k.kurikulum_id = kr.id_kurikulum
        ORDER BY k.tingkat, k.nama_kelas
    ");
    
    if (!empty($kelasWithKurikulumData)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Nama Kelas</th><th>Tingkat</th><th>Kurikulum</th><th>Wali Kelas</th></tr>";
        
        foreach ($kelasWithKurikulumData as $kelas) {
            $kurikulumDisplay = $kelas['nama_kurikulum'] ? 
                "{$kelas['nama_kurikulum']} ({$kelas['kode_kurikulum']})" : 
                '<span style="color: red;">Belum ada</span>';
                
            echo "<tr>";
            echo "<td>{$kelas['id_kelas']}</td>";
            echo "<td>{$kelas['nama_kelas']}</td>";
            echo "<td>{$kelas['tingkat']}</td>";
            echo "<td>$kurikulumDisplay</td>";
            echo "<td>{$kelas['wali_kelas']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Step 5: Final statistics
    echo "<h2>📊 Step 5: Final Statistics</h2>";
    
    $finalStats = $db->fetchAll("
        SELECT 
            kr.nama_kurikulum,
            kr.kode_kurikulum,
            COUNT(k.id) as jumlah_kelas
        FROM kurikulum kr
        LEFT JOIN kelas k ON kr.id_kurikulum = k.kurikulum_id
        GROUP BY kr.id_kurikulum, kr.nama_kurikulum, kr.kode_kurikulum
        ORDER BY kr.nama_kurikulum
    ");
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr><th>Kurikulum</th><th>Kode</th><th>Jumlah Kelas</th></tr>";
    
    $totalKelasWithKurikulum = 0;
    foreach ($finalStats as $stat) {
        echo "<tr>";
        echo "<td>{$stat['nama_kurikulum']}</td>";
        echo "<td>{$stat['kode_kurikulum']}</td>";
        echo "<td>{$stat['jumlah_kelas']}</td>";
        echo "</tr>";
        $totalKelasWithKurikulum += $stat['jumlah_kelas'];
    }
    echo "</table>";
    
    // Check for kelas without kurikulum
    $kelasWithoutKurikulumFinal = $db->fetchOne("SELECT COUNT(*) as count FROM kelas WHERE kurikulum_id IS NULL");
    
    if ($kelasWithoutKurikulumFinal['count'] > 0) {
        echo "<p style='color: orange;'>⚠️ Warning: {$kelasWithoutKurikulumFinal['count']} kelas masih belum memiliki kurikulum</p>";
    }
    
    echo "<hr>";
    echo "<h2>🎉 Update Complete!</h2>";
    echo "<p><strong>Summary:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Total Kelas: {$totalKelas['count']}</li>";
    echo "<li>✅ Kelas dengan Kurikulum: $totalKelasWithKurikulum</li>";
    echo "<li>✅ Kelas tanpa Kurikulum: {$kelasWithoutKurikulumFinal['count']}</li>";
    echo "</ul>";
    
    if ($kelasWithoutKurikulumFinal['count'] == 0) {
        echo "<p style='color: green; font-weight: bold;'>🎉 All kelas now have kurikulum assigned!</p>";
    }
    
    echo "<p>
        <a href='public/kelas' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>
            🏫 View Kelas
        </a>
        <a href='public/kurikulum' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>
            📚 Manage Kurikulum
        </a>
    </p>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h1 style='color: red;'>❌ Update Error</h1>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database connection and table structure.</p>";
}
?>
